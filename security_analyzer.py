#!/usr/bin/env python3
"""
Bloxsync Security Analysis Tool
Scans the codebase for potential security vulnerabilities and data leaks.
"""

import os
import re
import json
import sys
from pathlib import Path
from typing import List, Dict, Tuple, Any
from dataclasses import dataclass
from enum import Enum

class Severity(Enum):
    CRITICAL = "CRITICAL"
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"
    INFO = "INFO"

@dataclass
class SecurityFinding:
    file_path: str
    line_number: int
    severity: Severity
    category: str
    description: str
    code_snippet: str
    recommendation: str

class BloxsyncSecurityAnalyzer:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.findings: List[SecurityFinding] = []
        
        # Patterns for detecting security issues
        self.patterns = {
            'hardcoded_secrets': [
                (r'(?i)(api[_-]?key|secret|token|password|credential)\s*[:=]\s*["\']([^"\']{10,})["\']', 
                 "Potential hardcoded secret"),
                (r'(?i)(supabase[_-]?url|database[_-]?url)\s*[:=]\s*["\']([^"\']+)["\']', 
                 "Database URL exposure"),
                (r'(?i)(discord[_-]?webhook|webhook[_-]?url)\s*[:=]\s*["\']([^"\']+)["\']', 
                 "Discord webhook URL exposure"),
                (r'(?i)(service[_-]?role[_-]?key)\s*[:=]\s*["\']([^"\']+)["\']', 
                 "Service role key exposure"),
            ],
            'env_var_exposure': [
                (r'process\.env\.([A-Z_]+)', "Environment variable access"),
                (r'import\.meta\.env\.([A-Z_]+)', "Vite environment variable access"),
            ],
            'console_logs': [
                (r'console\.(log|error|warn|info|debug)\s*\([^)]*\)', "Console logging statement"),
            ],
            'client_side_validation': [
                (r'localStorage\.(getItem|setItem|removeItem)', "localStorage usage"),
                (r'sessionStorage\.(getItem|setItem|removeItem)', "sessionStorage usage"),
                (r'if\s*\([^)]*admin[^)]*\)\s*{', "Client-side admin check"),
            ],
            'database_exposure': [
                (r'\.rpc\s*\(\s*["\']([^"\']+)["\']', "RPC function call"),
                (r'supabase\s*\.\s*from\s*\(\s*["\']([^"\']+)["\']', "Direct table access"),
                (r'\.select\s*\(\s*["\']([^"\']*)["\']', "Database select operation"),
            ],
            'sensitive_data_transmission': [
                (r'fetch\s*\([^)]*\)', "HTTP request"),
                (r'axios\.(get|post|put|delete)', "Axios HTTP request"),
                (r'XMLHttpRequest', "XMLHttpRequest usage"),
            ]
        }

    def scan_file(self, file_path: Path) -> None:
        """Scan a single file for security issues."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                lines = content.split('\n')
                
            self._check_hardcoded_secrets(file_path, content, lines)
            self._check_bloxsync_specific_patterns(file_path, content, lines)
            self._check_environment_variables(file_path, content, lines)
            self._check_console_logs(file_path, content, lines)
            self._check_client_side_security(file_path, content, lines)
            self._check_database_patterns(file_path, content, lines)
            self._check_network_requests(file_path, content, lines)
            
        except Exception as e:
            print(f"Error scanning {file_path}: {e}")

    def _check_hardcoded_secrets(self, file_path: Path, content: str, lines: List[str]) -> None:
        """Check for hardcoded secrets and credentials."""
        for pattern, description in self.patterns['hardcoded_secrets']:
            for match in re.finditer(pattern, content, re.MULTILINE):
                line_num = content[:match.start()].count('\n') + 1
                code_snippet = lines[line_num - 1].strip() if line_num <= len(lines) else ""

                severity = Severity.CRITICAL
                if 'import.meta.env' in code_snippet or 'process.env' in code_snippet:
                    severity = Severity.LOW  # Environment variables are OK
                elif any(word in code_snippet.lower() for word in ['mock', 'test', 'example', 'placeholder']):
                    severity = Severity.LOW

                self.findings.append(SecurityFinding(
                    file_path=str(file_path.relative_to(self.project_root)),
                    line_number=line_num,
                    severity=severity,
                    category="Hardcoded Secrets",
                    description=description,
                    code_snippet=code_snippet,
                    recommendation="Move sensitive values to environment variables"
                ))

    def _check_bloxsync_specific_patterns(self, file_path: Path, content: str, lines: List[str]) -> None:
        """Check for Bloxsync-specific security patterns."""
        # Check for Discord webhook exposure
        discord_pattern = r'(discord[_-]?webhook|webhook[_-]?url)\s*[:=]\s*["\']([^"\']+)["\']'
        for match in re.finditer(discord_pattern, content, re.IGNORECASE):
            line_num = content[:match.start()].count('\n') + 1
            code_snippet = lines[line_num - 1].strip() if line_num <= len(lines) else ""

            if 'process.env' not in code_snippet:
                self.findings.append(SecurityFinding(
                    file_path=str(file_path.relative_to(self.project_root)),
                    line_number=line_num,
                    severity=Severity.CRITICAL,
                    category="Discord Webhook Exposure",
                    description="Discord webhook URL hardcoded in client-side code",
                    code_snippet=code_snippet,
                    recommendation="Move Discord webhook URL to server-side environment variables"
                ))

        # Check for HWID-related security issues
        hwid_patterns = [
            (r'localStorage\.(get|set)Item.*hwid', "HWID stored in localStorage"),
            (r'sessionStorage\.(get|set)Item.*hwid', "HWID stored in sessionStorage"),
            (r'hwid.*=.*["\'][^"\']+["\']', "Hardcoded HWID value")
        ]

        for pattern, desc in hwid_patterns:
            for match in re.finditer(pattern, content, re.IGNORECASE):
                line_num = content[:match.start()].count('\n') + 1
                code_snippet = lines[line_num - 1].strip() if line_num <= len(lines) else ""

                self.findings.append(SecurityFinding(
                    file_path=str(file_path.relative_to(self.project_root)),
                    line_number=line_num,
                    severity=Severity.HIGH,
                    category="HWID Security",
                    description=desc,
                    code_snippet=code_snippet,
                    recommendation="Use server-side HWID generation from IP address instead of client-side storage"
                ))

    def _check_environment_variables(self, file_path: Path, content: str, lines: List[str]) -> None:
        """Check for environment variable exposure patterns."""
        for pattern, description in self.patterns['env_var_exposure']:
            for match in re.finditer(pattern, content, re.MULTILINE):
                line_num = content[:match.start()].count('\n') + 1
                code_snippet = lines[line_num - 1].strip() if line_num <= len(lines) else ""
                var_name = match.group(1)
                
                severity = Severity.INFO
                if any(sensitive in var_name.lower() for sensitive in ['key', 'secret', 'token', 'password']):
                    if 'VITE_' in var_name:
                        severity = Severity.HIGH  # Client-side exposure of sensitive vars
                    else:
                        severity = Severity.MEDIUM
                
                self.findings.append(SecurityFinding(
                    file_path=str(file_path.relative_to(self.project_root)),
                    line_number=line_num,
                    severity=severity,
                    category="Environment Variables",
                    description=f"{description}: {var_name}",
                    code_snippet=code_snippet,
                    recommendation="Ensure sensitive environment variables are not exposed to client-side"
                ))

    def _check_console_logs(self, file_path: Path, content: str, lines: List[str]) -> None:
        """Check for console logging that might leak sensitive information."""
        for pattern, description in self.patterns['console_logs']:
            for match in re.finditer(pattern, content, re.MULTILINE):
                line_num = content[:match.start()].count('\n') + 1
                code_snippet = lines[line_num - 1].strip() if line_num <= len(lines) else ""
                
                severity = Severity.LOW
                if any(sensitive in code_snippet.lower() for sensitive in 
                       ['password', 'key', 'token', 'secret', 'hwid', 'admin']):
                    severity = Severity.MEDIUM
                
                self.findings.append(SecurityFinding(
                    file_path=str(file_path.relative_to(self.project_root)),
                    line_number=line_num,
                    severity=severity,
                    category="Console Logging",
                    description=description,
                    code_snippet=code_snippet,
                    recommendation="Remove console logs in production or ensure no sensitive data is logged"
                ))

    def _check_client_side_security(self, file_path: Path, content: str, lines: List[str]) -> None:
        """Check for client-side security anti-patterns."""
        for pattern, description in self.patterns['client_side_validation']:
            for match in re.finditer(pattern, content, re.MULTILINE):
                line_num = content[:match.start()].count('\n') + 1
                code_snippet = lines[line_num - 1].strip() if line_num <= len(lines) else ""
                
                severity = Severity.MEDIUM
                if 'admin' in code_snippet.lower():
                    severity = Severity.HIGH
                elif any(security_term in code_snippet.lower() for security_term in 
                        ['hwid', 'blacklist', 'banned', 'auth']):
                    severity = Severity.HIGH
                
                self.findings.append(SecurityFinding(
                    file_path=str(file_path.relative_to(self.project_root)),
                    line_number=line_num,
                    severity=severity,
                    category="Client-Side Security",
                    description=description,
                    code_snippet=code_snippet,
                    recommendation="Implement server-side validation for security-critical operations"
                ))

    def _check_database_patterns(self, file_path: Path, content: str, lines: List[str]) -> None:
        """Check for database access patterns that might expose sensitive data."""
        for pattern, description in self.patterns['database_exposure']:
            for match in re.finditer(pattern, content, re.MULTILINE):
                line_num = content[:match.start()].count('\n') + 1
                code_snippet = lines[line_num - 1].strip() if line_num <= len(lines) else ""
                
                severity = Severity.MEDIUM
                if 'admin' in code_snippet.lower() or 'rpc' in description.lower():
                    severity = Severity.HIGH
                
                self.findings.append(SecurityFinding(
                    file_path=str(file_path.relative_to(self.project_root)),
                    line_number=line_num,
                    severity=severity,
                    category="Database Access",
                    description=description,
                    code_snippet=code_snippet,
                    recommendation="Ensure proper RLS policies and avoid exposing sensitive database operations"
                ))

    def _check_network_requests(self, file_path: Path, content: str, lines: List[str]) -> None:
        """Check for network requests that might transmit sensitive data."""
        for pattern, description in self.patterns['sensitive_data_transmission']:
            for match in re.finditer(pattern, content, re.MULTILINE):
                line_num = content[:match.start()].count('\n') + 1
                code_snippet = lines[line_num - 1].strip() if line_num <= len(lines) else ""
                
                severity = Severity.INFO
                if any(sensitive in code_snippet.lower() for sensitive in 
                       ['admin', 'hwid', 'key', 'token']):
                    severity = Severity.MEDIUM
                
                self.findings.append(SecurityFinding(
                    file_path=str(file_path.relative_to(self.project_root)),
                    line_number=line_num,
                    severity=severity,
                    category="Network Requests",
                    description=description,
                    code_snippet=code_snippet,
                    recommendation="Ensure sensitive data is properly encrypted and validated server-side"
                ))

    def scan_directory(self) -> None:
        """Scan all relevant files in the project directory."""
        file_extensions = {'.js', '.jsx', '.ts', '.tsx', '.json', '.env', '.toml'}
        exclude_dirs = {'node_modules', '.git', 'dist', 'build', '.next'}
        
        for file_path in self.project_root.rglob('*'):
            if (file_path.is_file() and 
                file_path.suffix in file_extensions and
                not any(exclude_dir in file_path.parts for exclude_dir in exclude_dirs)):
                self.scan_file(file_path)

    def generate_report(self) -> Dict[str, Any]:
        """Generate a comprehensive security report."""
        # Sort findings by severity
        severity_order = {Severity.CRITICAL: 0, Severity.HIGH: 1, Severity.MEDIUM: 2, Severity.LOW: 3, Severity.INFO: 4}
        self.findings.sort(key=lambda x: (severity_order[x.severity], x.file_path, x.line_number))
        
        # Group findings by category and severity
        by_category = {}
        by_severity = {}
        
        for finding in self.findings:
            # By category
            if finding.category not in by_category:
                by_category[finding.category] = []
            by_category[finding.category].append(finding)
            
            # By severity
            if finding.severity not in by_severity:
                by_severity[finding.severity] = []
            by_severity[finding.severity].append(finding)
        
        return {
            'summary': {
                'total_findings': len(self.findings),
                'by_severity': {severity.value: len(findings) for severity, findings in by_severity.items()},
                'by_category': {category: len(findings) for category, findings in by_category.items()}
            },
            'findings': [
                {
                    'file_path': f.file_path,
                    'line_number': f.line_number,
                    'severity': f.severity.value,
                    'category': f.category,
                    'description': f.description,
                    'code_snippet': f.code_snippet,
                    'recommendation': f.recommendation
                }
                for f in self.findings
            ]
        }

def main():
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = '.'
    
    analyzer = BloxsyncSecurityAnalyzer(project_root)
    print(f"🔍 Scanning Bloxsync codebase at: {os.path.abspath(project_root)}")
    print("=" * 60)
    
    analyzer.scan_directory()
    report = analyzer.generate_report()
    
    # Print summary
    print(f"\n📊 SECURITY ANALYSIS SUMMARY")
    print("=" * 60)
    print(f"Total findings: {report['summary']['total_findings']}")
    print("\nBy Severity:")
    for severity, count in report['summary']['by_severity'].items():
        print(f"  {severity}: {count}")
    
    print("\nBy Category:")
    for category, count in report['summary']['by_category'].items():
        print(f"  {category}: {count}")
    
    # Print detailed findings
    print(f"\n🚨 DETAILED FINDINGS")
    print("=" * 60)
    
    for i, finding in enumerate(report['findings'], 1):
        severity_emoji = {
            'CRITICAL': '🔴',
            'HIGH': '🟠', 
            'MEDIUM': '🟡',
            'LOW': '🔵',
            'INFO': '⚪'
        }
        
        print(f"\n{i}. {severity_emoji[finding['severity']]} {finding['severity']} - {finding['category']}")
        print(f"   File: {finding['file_path']}:{finding['line_number']}")
        print(f"   Issue: {finding['description']}")
        print(f"   Code: {finding['code_snippet']}")
        print(f"   Fix: {finding['recommendation']}")
    
    # Save JSON report
    with open('security_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n💾 Detailed report saved to: security_report.json")
    
    # Exit with error code if critical/high severity issues found
    critical_high_count = report['summary']['by_severity'].get('CRITICAL', 0) + report['summary']['by_severity'].get('HIGH', 0)
    if critical_high_count > 0:
        print(f"\n⚠️  Found {critical_high_count} critical/high severity issues!")
        sys.exit(1)
    else:
        print(f"\n✅ No critical or high severity issues found.")
        sys.exit(0)

if __name__ == "__main__":
    main()
