{"summary": {"total_findings": 744, "by_severity": {"HIGH": 321, "MEDIUM": 321, "LOW": 67, "INFO": 35}, "by_category": {"Database Access": 393, "Client-Side Security": 160, "HWID Security": 72, "Environment Variables": 32, "Console Logging": 76, "Network Requests": 11}}, "findings": [{"file_path": "netlify/functions/getUserData.js", "line_number": 503, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 509, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 543, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 549, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 591, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 597, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 641, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 647, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 690, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 696, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 740, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 746, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 780, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 786, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 796, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('admin_keys').select('id'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 796, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('admin_keys').select('id'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 35, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"if (adminError || !adminCheck) {\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 53, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"if (adminError || !adminCheck) {\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 71, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"if (adminError || !adminCheck) {\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 89, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"if (adminError || !adminCheck) {\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 107, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"if (adminError || !adminCheck) {\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 125, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"if (adminError || !adminCheck) {\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 143, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"if (adminError || !adminCheck) {\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 152, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"supabase.from('admin_keys').select('id'),\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 152, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"supabase.from('admin_keys').select('id'),\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 161, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"supabase.from('admin_keys').select('id'),\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 161, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"supabase.from('admin_keys').select('id'),\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 170, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"if (adminError || !adminCheck) {\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 179, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"if (adminError || !adminCheck) {\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 188, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"if (adminError || !adminCheck) {\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 197, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"if (adminError || !adminCheck) {\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 206, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"if (adminError || !adminCheck) {\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 215, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"if (adminError || !adminCheck) {\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 224, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"if (adminError || !adminCheck) {\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 233, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('admin_keys').select('id'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 233, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('admin_keys').select('id'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 242, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('admin_keys').select('id'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 242, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('admin_keys').select('id'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 251, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('admin_keys').select('id'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 251, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('admin_keys').select('id'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 260, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('admin_keys').select('id'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 260, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('admin_keys').select('id'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 269, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"if (adminError || !adminCheck) {\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 278, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"if (adminError || !adminCheck) {\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 287, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"if (adminError || !adminCheck) {\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 296, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"if (adminError || !adminCheck) {\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 305, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"if (adminError || !adminCheck) {\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 314, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"if (adminError || !adminCheck) {\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 323, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"if (adminError || !adminCheck) {\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 332, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 332, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 341, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 341, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 350, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 350, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 359, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 359, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 368, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 368, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 377, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 377, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 386, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 386, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 395, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 395, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('admin_keys').select('id'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 404, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"if (location.pathname === '/admin/login') {\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 413, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 422, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 431, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"if (!isAuthenticated && location.pathname !== '/admin/login') {\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 440, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 449, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 458, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const name = sessionStorage.getItem('6footscripts_admin_name');\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 467, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"if (e.key === '6footscripts_admin_auth' || e.key === '6footscripts_admin_name') {\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 476, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const accessKey = sessionStorage.getItem('6footscripts_admin_key');\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 485, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 485, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 485, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 494, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 494, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 494, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 503, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 503, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 503, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 512, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 512, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 512, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 521, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 521, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 521, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 530, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 530, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 530, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 539, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 539, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 539, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 548, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 548, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 548, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 557, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 557, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 557, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 566, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 566, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 575, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 575, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 584, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 584, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 593, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 593, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 602, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 602, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 611, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 611, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 620, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 620, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 629, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 629, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 638, "severity": "HIGH", "category": "Environment Variables", "description": "Vite environment variable access: VITE_SUPABASE_ANON_KEY", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;\\\\\\\",\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 647, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"sessionStorage.removeItem('6footscripts_admin_auth');\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 656, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"sessionStorage.removeItem('6footscripts_admin_name');\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 665, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"sessionStorage.removeItem('6footscripts_admin_key');\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 674, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"sessionStorage.setItem('6footscripts_admin_auth', 'true');\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 683, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"sessionStorage.setItem('6footscripts_admin_name', result.name);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 692, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"sessionStorage.setItem('6footscripts_admin_key', accessKey);\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 701, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 701, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 710, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 710, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 719, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 719, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 728, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 728, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 737, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 737, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 746, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 746, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 755, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 755, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 764, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 764, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 773, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { error } = await supabase.from('admin_keys').insert([{ access_key: <PERSON><PERSON>ey }]);\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 782, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 782, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 791, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 791, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 800, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 800, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 809, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 809, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 818, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 818, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 827, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 827, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 836, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 836, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 845, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 845, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 854, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"sessionStorage.removeItem('6footscripts_admin_auth');\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 863, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"sessionStorage.removeItem('6footscripts_admin_name');\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 872, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"sessionStorage.removeItem('6footscripts_admin_key');\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 881, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"sessionStorage.removeItem('6footscripts_admin_auth');\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 890, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"if (location.pathname === '/admin/login') {\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 899, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 908, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 917, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"if (!isAuthenticated && location.pathname !== '/admin/login') {\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 926, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 935, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 944, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const name = sessionStorage.getItem('6footscripts_admin_name');\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 953, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"if (e.key === '6footscripts_admin_auth' || e.key === '6footscripts_admin_name') {\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 962, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const accessKey = sessionStorage.getItem('6footscripts_admin_key');\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 971, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const HWID_STORAGE_KEY = '6footscripts_hwid';\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 980, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 980, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 980, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 989, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 989, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 989, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 998, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 998, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 998, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1007, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1007, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1007, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1016, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1016, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1016, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1025, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1025, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1025, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1034, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1034, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1043, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1043, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1052, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1052, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1061, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1061, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1070, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"hwid = await sha256('127.0.0.1');\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1079, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1079, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1088, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1088, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1097, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1097, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1106, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1106, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(HWID_STORAGE_KEY, hwid);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1115, "severity": "HIGH", "category": "Environment Variables", "description": "Vite environment variable access: VITE_SUPABASE_ANON_KEY", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 1124, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const [hwid, setHwid] = React.useState('');\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1133, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const [newHwid, setNewHwid] = useState('');\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1142, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"<form onSubmit={handleAddHwid} className=\\\\\\\"space-y-4\\\\\\\">\\\",\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1151, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"sessionStorage.removeItem('6footscripts_admin_auth');\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1160, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"sessionStorage.removeItem('6footscripts_admin_name');\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1169, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"sessionStorage.removeItem('6footscripts_admin_key');\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1178, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"sessionStorage.setItem('6footscripts_admin_auth', 'true');\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1187, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"sessionStorage.setItem('6footscripts_admin_name', result.name);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1196, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"sessionStorage.setItem('6footscripts_admin_key', accessKey);\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1205, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1205, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1214, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1214, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1223, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1223, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1232, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1232, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1241, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { error } = await supabase.from('admin_keys').insert([{ access_key: generatedKey }]);\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1250, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1250, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1259, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1259, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1268, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1268, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1277, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1277, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1286, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"sessionStorage.removeItem('6footscripts_admin_auth');\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1295, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"sessionStorage.removeItem('6footscripts_admin_name');\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1304, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"sessionStorage.removeItem('6footscripts_admin_key');\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1313, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"sessionStorage.removeItem('6footscripts_admin_auth');\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1322, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"if (location.pathname === '/admin/login') {\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1331, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1340, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1349, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"if (!isAuthenticated && location.pathname !== '/admin/login') {\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1358, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1367, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1376, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"const name = sessionStorage.getItem('6footscripts_admin_name');\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1385, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "\"code_snippet\": \"if (e.key === '6footscripts_admin_auth' || e.key === '6footscripts_admin_name') {\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1394, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"const accessKey = sessionStorage.getItem('6footscripts_admin_key');\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1403, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"const HWID_STORAGE_KEY = '6footscripts_hwid';\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1412, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1412, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1412, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1421, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1421, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1421, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"let hwid = localStorage.getItem(HWID_STORAGE_KEY);\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1430, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"localStorage.setItem(HWID_STORAGE_KEY, hwid);\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1430, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"localStorage.setItem(HWID_STORAGE_KEY, hwid);\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1439, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"localStorage.setItem(HWID_STORAGE_KEY, hwid);\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1439, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"localStorage.setItem(HWID_STORAGE_KEY, hwid);\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1448, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"hwid = await sha256('127.0.0.1');\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1457, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"localStorage.setItem(HWID_STORAGE_KEY, hwid);\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1457, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"localStorage.setItem(HWID_STORAGE_KEY, hwid);\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1466, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "\"code_snippet\": \"localStorage.setItem(HWID_STORAGE_KEY, hwid);\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1466, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"localStorage.setItem(HWID_STORAGE_KEY, hwid);\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1475, "severity": "HIGH", "category": "Environment Variables", "description": "Vite environment variable access: VITE_SUPABASE_ANON_KEY", "code_snippet": "\"code_snippet\": \"const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 1484, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"const [hwid, setHwid] = React.useState('');\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1493, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"const [newHwid, setNewHwid] = useState('');\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1502, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "\"code_snippet\": \"<form onSubmit={handleAddHwid} className=\\\"space-y-4\\\">\",", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "security_report.json", "line_number": 1511, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"sessionStorage.removeItem('6footscripts_admin_auth');\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1520, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"sessionStorage.removeItem('6footscripts_admin_name');\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1529, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"sessionStorage.removeItem('6footscripts_admin_key');\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1538, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"sessionStorage.setItem('6footscripts_admin_auth', 'true');\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1547, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"sessionStorage.setItem('6footscripts_admin_name', result.name);\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1556, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"sessionStorage.setItem('6footscripts_admin_key', accessKey);\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1565, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1565, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1574, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1574, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1583, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"const { error } = await supabase.from('admin_keys').insert([{ access_key: generatedKey }]);\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1592, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1592, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1601, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1601, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1610, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"sessionStorage.removeItem('6footscripts_admin_auth');\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1619, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"sessionStorage.removeItem('6footscripts_admin_name');\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1628, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"sessionStorage.removeItem('6footscripts_admin_key');\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 1637, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "\"code_snippet\": \"sessionStorage.removeItem('6footscripts_admin_auth');\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/components/AdminAccessGuard.jsx", "line_number": 13, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (location.pathname === '/admin/login') {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/components/AdminAccessGuard.jsx", "line_number": 19, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/components/AdminAccessGuard.jsx", "line_number": 44, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/components/AdminAccessGuard.jsx", "line_number": 45, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (!isAuthenticated && location.pathname !== '/admin/login') {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/components/ProtectedRoute.jsx", "line_number": 6, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/contexts/AdminAuthContext.jsx", "line_number": 21, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/contexts/AdminAuthContext.jsx", "line_number": 22, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "const name = sessionStorage.getItem('6footscripts_admin_name');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/contexts/AdminAuthContext.jsx", "line_number": 33, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (e.key === '6footscripts_admin_auth' || e.key === '6footscripts_admin_name') {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/contexts/AdminAuthContext.jsx", "line_number": 46, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "const accessKey = sessionStorage.getItem('6footscripts_admin_key');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/lib/hwid.js", "line_number": 1, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "const HWID_STORAGE_KEY = '6footscripts_hwid';", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/lib/hwid.js", "line_number": 40, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "let hwid = localStorage.getItem(HWID_STORAGE_KEY);", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/lib/hwid.js", "line_number": 40, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "let hwid = localStorage.getItem(HWID_STORAGE_KEY);", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/lib/hwid.js", "line_number": 49, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "localStorage.setItem(HWID_STORAGE_KEY, hwid);", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/lib/hwid.js", "line_number": 49, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "localStorage.setItem(HWID_STORAGE_KEY, hwid);", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/lib/hwid.js", "line_number": 51, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "hwid = await sha256('127.0.0.1');", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/lib/hwid.js", "line_number": 52, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "localStorage.setItem(HWID_STORAGE_KEY, hwid);", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/lib/hwid.js", "line_number": 52, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "localStorage.setItem(HWID_STORAGE_KEY, hwid);", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/lib/supabaseClient.js", "line_number": 4, "severity": "HIGH", "category": "Environment Variables", "description": "Vite environment variable access: VITE_SUPABASE_ANON_KEY", "code_snippet": "const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "src/pages/GetHwid.jsx", "line_number": 15, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "const [hwid, setHwid] = React.useState('');", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/pages/admin/AdminBlacklist.jsx", "line_number": 15, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "const [newHwid, setNewHwid] = useState('');", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/pages/admin/AdminBlacklist.jsx", "line_number": 76, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "<form onSubmit={handleAddHwid} className=\"space-y-4\">", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/pages/admin/AdminLogin.jsx", "line_number": 21, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.removeItem('6footscripts_admin_auth');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/AdminLogin.jsx", "line_number": 22, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.removeItem('6footscripts_admin_name');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/AdminLogin.jsx", "line_number": 23, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.removeItem('6footscripts_admin_key');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/AdminLogin.jsx", "line_number": 40, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.setItem('6footscripts_admin_auth', 'true');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/AdminLogin.jsx", "line_number": 41, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.setItem('6footscripts_admin_name', result.name);", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/AdminLogin.jsx", "line_number": 42, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.setItem('6footscripts_admin_key', accessKey);", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/AdminPanel.jsx", "line_number": 23, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminPanel.jsx", "line_number": 23, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminPanel.jsx", "line_number": 40, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error } = await supabase.from('admin_keys').insert([{ access_key: generatedKey }]);", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 23, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 23, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/layout/AdminHeader.jsx", "line_number": 23, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.removeItem('6footscripts_admin_auth');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/layout/AdminHeader.jsx", "line_number": 24, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.removeItem('6footscripts_admin_name');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/layout/AdminHeader.jsx", "line_number": 25, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.removeItem('6footscripts_admin_key');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/layout/AdminSidebar.jsx", "line_number": 23, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.removeItem('6footscripts_admin_auth');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 5, "severity": "MEDIUM", "category": "Environment Variables", "description": "Environment variable access: SUPABASE_SERVICE_ROLE_KEY", "code_snippet": "const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 151, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 184, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 215, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 217, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('script_id, rating')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 249, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 313, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error: upsertError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 327, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 355, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 357, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('hwid')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 390, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 392, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id, name')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 429, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: keyData, error: keyError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 431, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id, name, hwid')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 444, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error: updateError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 505, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 517, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 519, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('*')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 545, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 557, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 559, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('*')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 593, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 605, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 643, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 655, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 692, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 704, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 742, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 754, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 782, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 795, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('script_requests').select('id, created_at, status'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 795, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('script_requests').select('id, created_at, status'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 797, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('page_visits').select('id, created_at'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 797, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('page_visits').select('id, created_at'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 798, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('scripts').select('id, created_at')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 798, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('scripts').select('id, created_at')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1646, "severity": "MEDIUM", "category": "Environment Variables", "description": "Environment variable access: SUPABASE_SERVICE_ROLE_KEY", "code_snippet": "\"code_snippet\": \"const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 1682, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \".select('script_id, rating')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1727, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \".select('hwid')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1745, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \".select('id, name')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1763, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \".select('id, name, hwid')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1781, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \".select('id')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1799, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \".select('*')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1808, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \".select('id')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1826, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \".select('*')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1835, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \".select('id')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1853, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \".select('id')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1871, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \".select('id')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1889, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \".select('id')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1907, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \".select('id')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1916, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"supabase.from('script_requests').select('id, created_at, status'),\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1916, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"supabase.from('script_requests').select('id, created_at, status'),\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1925, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"supabase.from('script_requests').select('id, created_at, status'),\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1925, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"supabase.from('script_requests').select('id, created_at, status'),\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1934, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"supabase.from('page_visits').select('id, created_at'),\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1934, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"supabase.from('page_visits').select('id, created_at'),\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1943, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"supabase.from('page_visits').select('id, created_at'),\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1943, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"supabase.from('page_visits').select('id, created_at'),\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1952, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"supabase.from('scripts').select('id, created_at')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1952, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"supabase.from('scripts').select('id, created_at')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1961, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"supabase.from('scripts').select('id, created_at')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1961, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"supabase.from('scripts').select('id, created_at')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1970, "severity": "MEDIUM", "category": "Environment Variables", "description": "Environment variable access: SUPABASE_SERVICE_ROLE_KEY", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 1979, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\".select('script_id, rating')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1988, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\".select('hwid')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 1997, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\".select('id, name')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2006, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\".select('id, name, hwid')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2015, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\".select('id')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2024, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\".select('*')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2033, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\".select('id')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2042, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\".select('*')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2051, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\".select('id')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2060, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\".select('id')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2069, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\".select('id')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2078, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\".select('id')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2087, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\".select('id')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2096, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('script_requests').select('id, created_at, status'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2096, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('script_requests').select('id, created_at, status'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2105, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('script_requests').select('id, created_at, status'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2105, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('script_requests').select('id, created_at, status'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2114, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('script_requests').select('id, created_at, status'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2114, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('script_requests').select('id, created_at, status'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2123, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('script_requests').select('id, created_at, status'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2123, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('script_requests').select('id, created_at, status'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2132, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('page_visits').select('id, created_at'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2132, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('page_visits').select('id, created_at'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2141, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('page_visits').select('id, created_at'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2141, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('page_visits').select('id, created_at'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2150, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('page_visits').select('id, created_at'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2150, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('page_visits').select('id, created_at'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2159, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('page_visits').select('id, created_at'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2159, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('page_visits').select('id, created_at'),\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2168, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('scripts').select('id, created_at')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2168, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('scripts').select('id, created_at')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2177, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('scripts').select('id, created_at')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2177, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('scripts').select('id, created_at')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2186, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('scripts').select('id, created_at')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2186, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('scripts').select('id, created_at')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2195, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('scripts').select('id, created_at')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2195, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"supabase.from('scripts').select('id, created_at')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2204, "severity": "MEDIUM", "category": "Environment Variables", "description": "Environment variable access: SUPABASE_SERVICE_ROLE_KEY", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\\\\\\\",\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 2213, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\".select('script_id, rating')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2222, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\".select('hwid')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2231, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\".select('id, name')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2240, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\".select('id, name, hwid')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2249, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\".select('id')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2258, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\".select('*')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2267, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\".select('id')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2276, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\".select('*')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2285, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\".select('id')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2294, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\".select('id')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2303, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\".select('id')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2312, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\".select('id')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2321, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\".select('id')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2330, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2330, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2339, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2339, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2348, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2348, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2357, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2357, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2366, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2366, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2375, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2375, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2384, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2384, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2393, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2393, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('script_requests').select('id, created_at, status'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2402, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2402, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2411, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2411, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2420, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2420, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2429, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2429, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2438, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2438, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2447, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2447, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2456, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2456, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2465, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2465, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('page_visits').select('id, created_at'),\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2474, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2474, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2483, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2483, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2492, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2492, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2501, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2501, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2510, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2510, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2519, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2519, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2528, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2528, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2537, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2537, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"supabase.from('scripts').select('id, created_at')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2546, "severity": "MEDIUM", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error('Admin check failed:', error);\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 2555, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"() => localStorage.getItem(storageKey) || defaultTheme\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 2564, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem(storageKey, theme)\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 2573, "severity": "MEDIUM", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.warn('Supabase environment variables missing. Admin functions will not work.');\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 2582, "severity": "MEDIUM", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error('Failed to check admin status:', error);\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 2591, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const storedRequests = JSON.parse(localStorage.getItem('scriptRequests') || '[]');\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 2600, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"localStorage.setItem('scriptRequests', JSON.stringify(updatedRequests));\\\\\\\",\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 2609, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2609, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2618, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2618, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2627, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2627, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2636, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2636, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2645, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2645, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2654, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2654, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2663, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2663, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2672, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2672, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2681, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { error } = await supabase.from('blacklisted_hwids').insert([{ hwid: newHwid, reason: newReason }]);\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2690, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { error } = await supabase.from('blacklisted_hwids').delete().eq('id', id);\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2699, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\".select('*')\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2708, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2708, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2717, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2717, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2726, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2726, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2735, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2735, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2744, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2744, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2753, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2753, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2762, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2762, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2771, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2771, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2780, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2780, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2789, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2789, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2798, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2798, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2807, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2807, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2816, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2816, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2825, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2825, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2834, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2834, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2843, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2843, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2852, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2852, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2861, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2861, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2870, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2870, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2879, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2879, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2888, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2888, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2897, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2897, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2906, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2906, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2915, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2915, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\\\\\",\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2924, "severity": "MEDIUM", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error('Admin check failed:', error);\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 2933, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"() => localStorage.getItem(storageKey) || defaultTheme\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 2942, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem(storageKey, theme)\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 2951, "severity": "MEDIUM", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error('Failed to check admin status:', error);\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 2960, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const storedRequests = JSON.parse(localStorage.getItem('scriptRequests') || '[]');\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 2969, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"localStorage.setItem('scriptRequests', JSON.stringify(updatedRequests));\\\",\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 2978, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2978, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2987, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2987, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2996, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 2996, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3005, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3005, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3014, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { error } = await supabase.from('blacklisted_hwids').insert([{ hwid: newHwid, reason: newReason }]);\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3023, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { error } = await supabase.from('blacklisted_hwids').delete().eq('id', id);\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3032, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\".select('*')\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3041, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3041, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3050, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3050, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3059, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3059, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3068, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3068, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3077, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3077, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3086, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3086, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3095, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3095, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3104, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3104, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3113, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3113, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3122, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3122, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3131, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3131, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3140, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3140, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\\\",\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3149, "severity": "MEDIUM", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error('Admin check failed:', error);\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3158, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"() => localStorage.getItem(storageKey) || defaultTheme\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 3167, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"localStorage.setItem(storageKey, theme)\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 3176, "severity": "MEDIUM", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error('Failed to check admin status:', error);\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3185, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"const storedRequests = JSON.parse(localStorage.getItem('scriptRequests') || '[]');\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 3194, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "\"code_snippet\": \"localStorage.setItem('scriptRequests', JSON.stringify(updatedRequests));\",", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "security_report.json", "line_number": 3203, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3203, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3212, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3212, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3221, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"const { error } = await supabase.from('blacklisted_hwids').insert([{ hwid: newHwid, reason: newReason }]);\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3230, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"const { error } = await supabase.from('blacklisted_hwids').delete().eq('id', id);\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3248, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \".select('*')\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3266, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3266, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3275, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3275, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3284, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3284, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3293, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3293, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3302, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3302, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3311, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "\"code_snippet\": \"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "security_report.json", "line_number": 3311, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "\"code_snippet\": \"const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');\",", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/components/AdminAccessGuard.jsx", "line_number": 28, "severity": "MEDIUM", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Admin check failed:', error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "src/contexts/ThemeProvider.jsx", "line_number": 17, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "() => localStorage.getItem(storageKey) || defaultTheme", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/contexts/ThemeProvider.jsx", "line_number": 41, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "localStorage.setItem(storageKey, theme)", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/Home.jsx", "line_number": 21, "severity": "MEDIUM", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Failed to check admin status:', error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "src/pages/ScriptRequests.jsx", "line_number": 15, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "const storedRequests = JSON.parse(localStorage.getItem('scriptRequests') || '[]');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/ScriptRequests.jsx", "line_number": 28, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "localStorage.setItem('scriptRequests', JSON.stringify(updatedRequests));", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/AdminBlacklist.jsx", "line_number": 22, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminBlacklist.jsx", "line_number": 22, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminBlacklist.jsx", "line_number": 42, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error } = await supabase.from('blacklisted_hwids').insert([{ hwid: newHwid, reason: newReason }]);", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminBlacklist.jsx", "line_number": 55, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error } = await supabase.from('blacklisted_hwids').delete().eq('id', id);", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminPanel.jsx", "line_number": 109, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminPanel.jsx", "line_number": 111, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('*')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminPanel.jsx", "line_number": 138, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 22, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 22, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 24, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 24, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 26, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 26, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 8, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Missing Supabase environment variables');", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 20, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Supabase initialization error:', error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 185, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Unauthorized parent origin:', parent<PERSON><PERSON>in);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 188, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error saving changes: ${result.error}`);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 191, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error during fetch for ${editId}:`, error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/vite-plugin-react-inline-editor.js", "line_number": 206, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error transforming ${id}:`, error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/vite-plugin-react-inline-editor.js", "line_number": 305, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error during direct write for ${filePath}:`, writeError);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3320, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error('Missing Supabase environment variables');\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3329, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error('Supabase initialization error:', error);\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3338, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error('Unauthorized parent origin:', parentO<PERSON>in);\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3347, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error(`[vite][visual-editor] Error saving changes: ${result.error}`);\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3356, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error(`[vite][visual-editor] Error during fetch for ${editId}:`, error);\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3365, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error(`[vite][visual-editor] Error transforming ${id}:`, error);\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3374, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error(`[vite][visual-editor] Error during direct write for ${filePath}:`, writeError);\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3383, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error('Missing Supabase environment variables');\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3392, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error('Supabase initialization error:', error);\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3401, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error('Unauthorized parent origin:', parent<PERSON><PERSON>in);\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3410, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error(`[vite][visual-editor] Error saving changes: ${result.error}`);\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3419, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error(`[vite][visual-editor] Error during fetch for ${editId}:`, error);\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3428, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error(`[vite][visual-editor] Error transforming ${id}:`, error);\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3437, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error(`[vite][visual-editor] Error during direct write for ${filePath}:`, writeError);\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3446, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error('Missing Supabase environment variables');\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3455, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error('Supabase initialization error:', error);\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3464, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.warn('[INLINE EDITOR] Clicked element missing data-edit-id');\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3473, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.warn('Invalid referrer URL:', document.referrer);\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3482, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error('Unauthorized parent origin:', parent<PERSON><PERSON>in);\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3491, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error(`[vite][visual-editor] Error saving changes: ${result.error}`);\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3500, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error(`[vite][visual-editor] Error during fetch for ${editId}:`, error);\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3509, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error(`[vite][visual-editor] Error transforming ${id}:`, error);\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3518, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error(`[vite][visual-editor] Error during direct write for ${filePath}:`, writeError);\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3527, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error(\\\\\\\\\\\\\\\"Uncaught error:\\\\\\\\\\\\\\\", error, errorInfo);\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3536, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error(\\\\\\\\\\\\\\\"Failed to parse script requests from localStorage\\\\\\\\\\\\\\\", error);\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3545, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error('Error fetching scripts:', error);\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3554, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error(`\\\\\\\\u274c Error processing ${filePath}:`, error.message);\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3563, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error('Error: src/pages directory not found!');\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3572, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error('Make sure you\\\\\\\\\\\\\\\\'re running this script from your project root.');\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3581, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error('\\\\\\\\u274c No pages with Helmet components found!');\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3590, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.log('\\\\\\\\u2705 Successfully generated llms.txt!');\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3599, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error(\\\\\\\\\\\\\\\\`Fetch error from \\\\\\\\\\\\\\\\${requestUrl}: \\\\\\\\\\\\\\\\${errorFromRes}\\\\\\\\\\\\\\\\`);\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3608, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"console.error(error);\\\\\\\",\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3617, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error(\\\\\\\"Uncaught error:\\\\\\\", error, errorInfo);\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3626, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error(\\\\\\\"Failed to parse script requests from localStorage\\\\\\\", error);\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3635, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error('Error fetching scripts:', error);\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3644, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error(`\\\\u274c Error processing ${filePath}:`, error.message);\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3653, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error('Error: src/pages directory not found!');\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3662, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error('Make sure you\\\\\\\\'re running this script from your project root.');\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3671, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error('\\\\u274c No pages with Helmet components found!');\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3680, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error(\\\\\\\\`Fetch error from \\\\\\\\${requestUrl}: \\\\\\\\${errorFromRes}\\\\\\\\`);\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3689, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"console.error(error);\\\",\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3698, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error(\\\"Uncaught error:\\\", error, errorInfo);\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3707, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error(\\\"Failed to parse script requests from localStorage\\\", error);\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3716, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error('Error fetching scripts:', error);\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3725, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error(`\\u274c Error processing ${filePath}:`, error.message);\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3734, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error('Error: src/pages directory not found!');\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3743, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error('Make sure you\\\\'re running this script from your project root.');\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3752, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error('\\u274c No pages with Helmet components found!');\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3761, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error(\\\\`Fetch error from \\\\${requestUrl}: \\\\${errorFromRes}\\\\`);\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "security_report.json", "line_number": 3770, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "\"code_snippet\": \"console.error(error);\",", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "src/components/ErrorBoundary.jsx", "line_number": 15, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(\"Uncaught error:\", error, errorInfo);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "src/pages/ScriptRequests.jsx", "line_number": 18, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(\"Failed to parse script requests from localStorage\", error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "src/pages/Scripts.jsx", "line_number": 74, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Error fetching scripts:', error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 147, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`❌ Error processing ${filePath}:`, error.message);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 157, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Error: src/pages directory not found!');", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 158, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Make sure you\\'re running this script from your project root.');", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 169, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('❌ No pages with Helmet components found!');", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "vite.config.js", "line_number": 129, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(\\`Fetch error from \\${requestUrl}: \\${errorFromRes}\\`);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "vite.config.js", "line_number": 136, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 4, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: SUPABASE_URL", "code_snippet": "const supabaseUrl = process.env.SUPABASE_URL;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 40, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: ALLOWED_ORIGIN", "code_snippet": "'Access-Control-Allow-Origin': process.env.ALLOWED_ORIGIN || 'https://senthelptakoisputtingmeonbasement.netlify.app',", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 114, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: NODE_ENV", "code_snippet": "nodeEnv: process.env.NODE_ENV", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 264, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: DISCORD_WEBHOOK_URL", "code_snippet": "const webhookUrl = process.env.DISCORD_WEBHOOK_URL;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 284, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "await fetch(webhookUrl, {", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 160, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "const response = await fetch(PLUGIN_APPLY_EDIT_API_URL, {", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "security_report.json", "line_number": 3779, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: SUPABASE_URL", "code_snippet": "\"code_snippet\": \"const supabaseUrl = process.env.SUPABASE_URL;\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3788, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: ALLOWED_ORIGIN", "code_snippet": "\"code_snippet\": \"'Access-Control-Allow-Origin': process.env.ALLOWED_ORIGIN || 'https://senthelptakoisputtingmeonbasement.netlify.app',\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3797, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: NODE_ENV", "code_snippet": "\"code_snippet\": \"nodeEnv: process.env.NODE_ENV\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3806, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: DISCORD_WEBHOOK_URL", "code_snippet": "\"code_snippet\": \"const webhookUrl = process.env.DISCORD_WEBHOOK_URL;\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3815, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "\"code_snippet\": \"await fetch(webhookUrl, {\",", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "security_report.json", "line_number": 3833, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: SUPABASE_URL", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const supabaseUrl = process.env.SUPABASE_URL;\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3842, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: ALLOWED_ORIGIN", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"'Access-Control-Allow-Origin': process.env.ALLOWED_ORIGIN || 'https://senthelptakoisputtingmeonbasement.netlify.app',\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3851, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: NODE_ENV", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"nodeEnv: process.env.NODE_ENV\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3860, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: DISCORD_WEBHOOK_URL", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const webhookUrl = process.env.DISCORD_WEBHOOK_URL;\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3878, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: SUPABASE_URL", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const supabaseUrl = process.env.SUPABASE_URL;\\\\\\\",\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3887, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: ALLOWED_ORIGIN", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"'Access-Control-Allow-Origin': process.env.ALLOWED_ORIGIN || 'https://senthelptakoisputtingmeonbasement.netlify.app',\\\\\\\",\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3896, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: NODE_ENV", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"nodeEnv: process.env.NODE_ENV\\\\\\\",\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3905, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: DISCORD_WEBHOOK_URL", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const webhookUrl = process.env.DISCORD_WEBHOOK_URL;\\\\\\\",\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3914, "severity": "INFO", "category": "Environment Variables", "description": "Vite environment variable access: VITE_SUPABASE_URL", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;\\\\\\\",\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3923, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: NODE_ENV", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"\\\\\\\"code_snippet\\\\\\\": \\\\\\\"const isDev = process.env.NODE_ENV !== 'production';\\\\\\\",\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3941, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const response = await fetch(service);\\\",\",", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "security_report.json", "line_number": 3950, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const response = await fetch(url, config);\\\",\",", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "security_report.json", "line_number": 3959, "severity": "INFO", "category": "Environment Variables", "description": "Vite environment variable access: VITE_SUPABASE_URL", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3968, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: NODE_ENV", "code_snippet": "\"code_snippet\": \"\\\"code_snippet\\\": \\\"const isDev = process.env.NODE_ENV !== 'production';\\\",\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 3977, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "\"code_snippet\": \"const response = await fetch(url, config);\",", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "security_report.json", "line_number": 3986, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "\"code_snippet\": \"const response = await fetch(service);\",", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "security_report.json", "line_number": 3995, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "\"code_snippet\": \"const response = await fetch(url, config);\",", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "security_report.json", "line_number": 4004, "severity": "INFO", "category": "Environment Variables", "description": "Vite environment variable access: VITE_SUPABASE_URL", "code_snippet": "\"code_snippet\": \"const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "security_report.json", "line_number": 4013, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: NODE_ENV", "code_snippet": "\"code_snippet\": \"const isDev = process.env.NODE_ENV !== 'production';\",", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "src/lib/apiClient.js", "line_number": 14, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "const response = await fetch(url, config);", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "src/lib/hwid.js", "line_number": 21, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "const response = await fetch(service);", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "src/lib/secureApiClient.js", "line_number": 25, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "const response = await fetch(url, config);", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "src/lib/supabaseClient.js", "line_number": 3, "severity": "INFO", "category": "Environment Variables", "description": "Vite environment variable access: VITE_SUPABASE_URL", "code_snippet": "const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "vite.config.js", "line_number": 5, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: NODE_ENV", "code_snippet": "const isDev = process.env.NODE_ENV !== 'production';", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}]}