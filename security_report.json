{"summary": {"total_findings": 62, "by_severity": {"HIGH": 4, "MEDIUM": 37, "LOW": 11, "INFO": 10}, "by_category": {"Database Access": 37, "Environment Variables": 8, "Client-Side Security": 2, "Console Logging": 11, "Network Requests": 4}}, "findings": [{"file_path": "netlify/functions/getUserData.js", "line_number": 45, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: adminData, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 774, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('admin_keys').select('id'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 774, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('admin_keys').select('id'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/lib/supabaseClient.js", "line_number": 4, "severity": "HIGH", "category": "Environment Variables", "description": "Vite environment variable access: VITE_SUPABASE_ANON_KEY", "code_snippet": "const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 5, "severity": "MEDIUM", "category": "Environment Variables", "description": "Environment variable access: SUPABASE_SERVICE_ROLE_KEY", "code_snippet": "const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 47, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id, name')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 185, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 218, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 249, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 251, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('script_id, rating')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 283, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 347, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error: upsertError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 361, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 389, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 391, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('hwid')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 424, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 426, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id, name')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 463, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: keyData, error: keyError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 465, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id, name, hwid')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 478, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error: updateError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 543, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 545, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('*')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 575, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 577, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('*')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 615, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 657, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 698, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 740, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 773, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('script_requests').select('id, created_at, status'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 773, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('script_requests').select('id, created_at, status'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 775, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('page_visits').select('id, created_at'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 775, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('page_visits').select('id, created_at'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 776, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('scripts').select('id, created_at')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 776, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('scripts').select('id, created_at')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 838, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 871, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 873, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('*')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 903, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 936, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/ScriptRequests.jsx", "line_number": 15, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "const storedRequests = JSON.parse(localStorage.getItem('scriptRequests') || '[]');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/ScriptRequests.jsx", "line_number": 27, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "localStorage.setItem('scriptRequests', JSON.stringify(updatedRequests));", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 185, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Unauthorized parent origin:', parent<PERSON><PERSON>in);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 188, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error saving changes: ${result.error}`);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 191, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error during fetch for ${editId}:`, error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/vite-plugin-react-inline-editor.js", "line_number": 206, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error transforming ${id}:`, error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/vite-plugin-react-inline-editor.js", "line_number": 305, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error during direct write for ${filePath}:`, writeError);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 147, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`❌ Error processing ${filePath}:`, error.message);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 157, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Error: src/pages directory not found!');", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 158, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Make sure you\\'re running this script from your project root.');", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 169, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('❌ No pages with Helmet components found!');", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "vite.config.js", "line_number": 129, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(\\`Fetch error from \\${requestUrl}: \\${errorFromRes}\\`);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "vite.config.js", "line_number": 136, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 4, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: SUPABASE_URL", "code_snippet": "const supabaseUrl = process.env.SUPABASE_URL;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 62, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: ALLOWED_ORIGIN", "code_snippet": "'Access-Control-Allow-Origin': process.env.ALLOWED_ORIGIN || 'https://senthelptakoisputtingmeonbasement.netlify.app',", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 148, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: NODE_ENV", "code_snippet": "nodeEnv: process.env.NODE_ENV", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 298, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: DISCORD_WEBHOOK_URL", "code_snippet": "const webhookUrl = process.env.DISCORD_WEBHOOK_URL;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 318, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "await fetch(webhookUrl, {", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 160, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "const response = await fetch(PLUGIN_APPLY_EDIT_API_URL, {", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "src/lib/apiClient.js", "line_number": 14, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "const response = await fetch(url, config);", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "src/lib/secureApiClient.js", "line_number": 25, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "const response = await fetch(url, config);", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "src/lib/supabaseClient.js", "line_number": 3, "severity": "INFO", "category": "Environment Variables", "description": "Vite environment variable access: VITE_SUPABASE_URL", "code_snippet": "const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "vite.config.js", "line_number": 5, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: NODE_ENV", "code_snippet": "const isDev = process.env.NODE_ENV !== 'production';", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}]}