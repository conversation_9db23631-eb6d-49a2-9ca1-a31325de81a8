{"summary": {"total_findings": 134, "by_severity": {"HIGH": 52, "MEDIUM": 55, "LOW": 16, "INFO": 11}, "by_category": {"Database Access": 62, "Client-Side Security": 33, "HWID Security": 8, "Environment Variables": 8, "Console Logging": 18, "Network Requests": 5}}, "findings": [{"file_path": "netlify/functions/getUserData.js", "line_number": 503, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 509, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 543, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 549, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 591, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 597, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 641, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 647, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 690, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 696, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 740, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 746, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 780, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 786, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 796, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('admin_keys').select('id'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 796, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('admin_keys').select('id'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/components/AdminAccessGuard.jsx", "line_number": 13, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (location.pathname === '/admin/login') {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/components/AdminAccessGuard.jsx", "line_number": 19, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/components/AdminAccessGuard.jsx", "line_number": 44, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/components/AdminAccessGuard.jsx", "line_number": 45, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (!isAuthenticated && location.pathname !== '/admin/login') {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/components/ProtectedRoute.jsx", "line_number": 6, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/contexts/AdminAuthContext.jsx", "line_number": 21, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/contexts/AdminAuthContext.jsx", "line_number": 22, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "const name = sessionStorage.getItem('6footscripts_admin_name');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/contexts/AdminAuthContext.jsx", "line_number": 33, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (e.key === '6footscripts_admin_auth' || e.key === '6footscripts_admin_name') {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/contexts/AdminAuthContext.jsx", "line_number": 46, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "const accessKey = sessionStorage.getItem('6footscripts_admin_key');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/lib/hwid.js", "line_number": 1, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "const HWID_STORAGE_KEY = '6footscripts_hwid';", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/lib/hwid.js", "line_number": 40, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "let hwid = localStorage.getItem(HWID_STORAGE_KEY);", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/lib/hwid.js", "line_number": 40, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "let hwid = localStorage.getItem(HWID_STORAGE_KEY);", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/lib/hwid.js", "line_number": 49, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "localStorage.setItem(HWID_STORAGE_KEY, hwid);", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/lib/hwid.js", "line_number": 49, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "localStorage.setItem(HWID_STORAGE_KEY, hwid);", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/lib/hwid.js", "line_number": 51, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "hwid = await sha256('127.0.0.1');", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/lib/hwid.js", "line_number": 52, "severity": "HIGH", "category": "HWID Security", "description": "HWID stored in localStorage", "code_snippet": "localStorage.setItem(HWID_STORAGE_KEY, hwid);", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/lib/hwid.js", "line_number": 52, "severity": "HIGH", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "localStorage.setItem(HWID_STORAGE_KEY, hwid);", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/lib/supabaseClient.js", "line_number": 4, "severity": "HIGH", "category": "Environment Variables", "description": "Vite environment variable access: VITE_SUPABASE_ANON_KEY", "code_snippet": "const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "src/pages/GetHwid.jsx", "line_number": 15, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "const [hwid, setHwid] = React.useState('');", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/pages/admin/AdminBlacklist.jsx", "line_number": 15, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "const [newHwid, setNewHwid] = useState('');", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/pages/admin/AdminBlacklist.jsx", "line_number": 76, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "<form onSubmit={handleAddHwid} className=\"space-y-4\">", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/pages/admin/AdminLogin.jsx", "line_number": 21, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.removeItem('6footscripts_admin_auth');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/AdminLogin.jsx", "line_number": 22, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.removeItem('6footscripts_admin_name');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/AdminLogin.jsx", "line_number": 23, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.removeItem('6footscripts_admin_key');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/AdminLogin.jsx", "line_number": 40, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.setItem('6footscripts_admin_auth', 'true');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/AdminLogin.jsx", "line_number": 41, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.setItem('6footscripts_admin_name', result.name);", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/AdminLogin.jsx", "line_number": 42, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.setItem('6footscripts_admin_key', accessKey);", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/AdminPanel.jsx", "line_number": 23, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminPanel.jsx", "line_number": 23, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "const { data, error } = await supabase.from('admin_keys').select('*').order('created_at', { ascending: false });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminPanel.jsx", "line_number": 40, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error } = await supabase.from('admin_keys').insert([{ access_key: generatedKey }]);", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 23, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 23, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "const { count: keysCount } = await supabase.from('admin_keys').select('*', { count: 'exact', head: true });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/layout/AdminHeader.jsx", "line_number": 23, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.removeItem('6footscripts_admin_auth');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/layout/AdminHeader.jsx", "line_number": 24, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.removeItem('6footscripts_admin_name');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/layout/AdminHeader.jsx", "line_number": 25, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.removeItem('6footscripts_admin_key');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/layout/AdminSidebar.jsx", "line_number": 23, "severity": "HIGH", "category": "Client-Side Security", "description": "sessionStorage usage", "code_snippet": "sessionStorage.removeItem('6footscripts_admin_auth');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 5, "severity": "MEDIUM", "category": "Environment Variables", "description": "Environment variable access: SUPABASE_SERVICE_ROLE_KEY", "code_snippet": "const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 151, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 184, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 215, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 217, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('script_id, rating')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 249, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 313, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error: upsertError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 327, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 355, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 357, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('hwid')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 390, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 392, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id, name')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 429, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: keyData, error: keyError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 431, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id, name, hwid')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 444, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error: updateError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 505, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 517, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 519, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('*')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 545, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 557, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 559, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('*')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 593, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 605, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 643, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 655, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 692, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 704, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 742, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 754, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 782, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 795, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('script_requests').select('id, created_at, status'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 795, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('script_requests').select('id, created_at, status'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 797, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('page_visits').select('id, created_at'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 797, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('page_visits').select('id, created_at'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 798, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('scripts').select('id, created_at')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 798, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('scripts').select('id, created_at')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/components/AdminAccessGuard.jsx", "line_number": 28, "severity": "MEDIUM", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Admin check failed:', error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "src/contexts/ThemeProvider.jsx", "line_number": 17, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "() => localStorage.getItem(storageKey) || defaultTheme", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/contexts/ThemeProvider.jsx", "line_number": 41, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "localStorage.setItem(storageKey, theme)", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/Home.jsx", "line_number": 21, "severity": "MEDIUM", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Failed to check admin status:', error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "src/pages/ScriptRequests.jsx", "line_number": 15, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "const storedRequests = JSON.parse(localStorage.getItem('scriptRequests') || '[]');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/ScriptRequests.jsx", "line_number": 28, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "localStorage.setItem('scriptRequests', JSON.stringify(updatedRequests));", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/admin/AdminBlacklist.jsx", "line_number": 22, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminBlacklist.jsx", "line_number": 22, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "const { data, error } = await supabase.from('blacklisted_hwids').select('*').order('created_at', { ascending: false });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminBlacklist.jsx", "line_number": 42, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error } = await supabase.from('blacklisted_hwids').insert([{ hwid: newHwid, reason: newReason }]);", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminBlacklist.jsx", "line_number": 55, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error } = await supabase.from('blacklisted_hwids').delete().eq('id', id);", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminPanel.jsx", "line_number": 109, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminPanel.jsx", "line_number": 111, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('*')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/AdminPanel.jsx", "line_number": 138, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 22, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 22, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "const { count: requestsCount } = await supabase.from('script_requests').select('*', { count: 'exact', head: true });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 24, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 24, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "const { count: visitsCount } = await supabase.from('page_visits').select('*', { count: 'exact', head: true });", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 26, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/pages/admin/Dashboard.jsx", "line_number": 26, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "const { data: requestsData, error } = await supabase.from('script_requests').select('created_at, status');", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 8, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Missing Supabase environment variables');", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 20, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Supabase initialization error:', error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 185, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Unauthorized parent origin:', parent<PERSON><PERSON>in);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 188, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error saving changes: ${result.error}`);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 191, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error during fetch for ${editId}:`, error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/vite-plugin-react-inline-editor.js", "line_number": 206, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error transforming ${id}:`, error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/vite-plugin-react-inline-editor.js", "line_number": 305, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error during direct write for ${filePath}:`, writeError);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "src/components/ErrorBoundary.jsx", "line_number": 15, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(\"Uncaught error:\", error, errorInfo);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "src/pages/ScriptRequests.jsx", "line_number": 18, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(\"Failed to parse script requests from localStorage\", error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "src/pages/Scripts.jsx", "line_number": 74, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Error fetching scripts:', error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 147, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`❌ Error processing ${filePath}:`, error.message);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 157, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Error: src/pages directory not found!');", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 158, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Make sure you\\'re running this script from your project root.');", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 169, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('❌ No pages with Helmet components found!');", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "vite.config.js", "line_number": 129, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(\\`Fetch error from \\${requestUrl}: \\${errorFromRes}\\`);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "vite.config.js", "line_number": 136, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 4, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: SUPABASE_URL", "code_snippet": "const supabaseUrl = process.env.SUPABASE_URL;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 40, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: ALLOWED_ORIGIN", "code_snippet": "'Access-Control-Allow-Origin': process.env.ALLOWED_ORIGIN || 'https://senthelptakoisputtingmeonbasement.netlify.app',", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 114, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: NODE_ENV", "code_snippet": "nodeEnv: process.env.NODE_ENV", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 264, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: DISCORD_WEBHOOK_URL", "code_snippet": "const webhookUrl = process.env.DISCORD_WEBHOOK_URL;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 284, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "await fetch(webhookUrl, {", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 160, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "const response = await fetch(PLUGIN_APPLY_EDIT_API_URL, {", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "src/lib/apiClient.js", "line_number": 14, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "const response = await fetch(url, config);", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "src/lib/hwid.js", "line_number": 21, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "const response = await fetch(service);", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "src/lib/secureApiClient.js", "line_number": 25, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "const response = await fetch(url, config);", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "src/lib/supabaseClient.js", "line_number": 3, "severity": "INFO", "category": "Environment Variables", "description": "Vite environment variable access: VITE_SUPABASE_URL", "code_snippet": "const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "vite.config.js", "line_number": 5, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: NODE_ENV", "code_snippet": "const isDev = process.env.NODE_ENV !== 'production';", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}]}