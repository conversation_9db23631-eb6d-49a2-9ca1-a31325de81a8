{"summary": {"total_findings": 105, "by_severity": {"HIGH": 29, "MEDIUM": 50, "LOW": 16, "INFO": 10}, "by_category": {"Database Access": 57, "Client-Side Security": 16, "Environment Variables": 8, "HWID Security": 3, "Console Logging": 17, "Network Requests": 4}}, "findings": [{"file_path": "netlify/functions/getUserData.js", "line_number": 515, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 521, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 555, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 561, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 603, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 609, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 653, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 659, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 702, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 708, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 752, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 758, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 792, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 798, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 808, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('admin_keys').select('id'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 808, "severity": "HIGH", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('admin_keys').select('id'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 866, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 872, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 907, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 913, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 947, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 953, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 988, "severity": "HIGH", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: admin<PERSON>heck, error: adminError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 994, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (adminError || !adminCheck) {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/components/AdminAccessGuard.jsx", "line_number": 14, "severity": "HIGH", "category": "Client-Side Security", "description": "Client-side admin check", "code_snippet": "if (location.pathname === '/admin/login') {", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/lib/supabaseClient.js", "line_number": 4, "severity": "HIGH", "category": "Environment Variables", "description": "Vite environment variable access: VITE_SUPABASE_ANON_KEY", "code_snippet": "const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "src/pages/GetHwid.jsx", "line_number": 15, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "const [hwid, setHwid] = React.useState('');", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/pages/admin/AdminBlacklist.jsx", "line_number": 15, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "const [newHwid, setNewHwid] = useState('');", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "src/pages/admin/AdminBlacklist.jsx", "line_number": 77, "severity": "HIGH", "category": "HWID Security", "description": "Hardcoded HWID value", "code_snippet": "<form onSubmit={handleAddHwid} className=\"space-y-4\">", "recommendation": "Use server-side HWID generation from IP address instead of client-side storage"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 5, "severity": "MEDIUM", "category": "Environment Variables", "description": "Environment variable access: SUPABASE_SERVICE_ROLE_KEY", "code_snippet": "const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 163, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 196, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 227, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 229, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('script_id, rating')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 261, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 325, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error: upsertError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 339, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 367, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 369, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('hwid')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 402, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 404, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id, name')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 441, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data: keyData, error: keyError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 443, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id, name, hwid')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 456, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error: updateError } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 517, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 529, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 531, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('*')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 557, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 569, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 571, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('*')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 605, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 617, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 655, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 667, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 704, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 716, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 754, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 766, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 794, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 807, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('script_requests').select('id, created_at, status'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 807, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('script_requests').select('id, created_at, status'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 809, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('page_visits').select('id, created_at'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 809, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('page_visits').select('id, created_at'),", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 810, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "supabase.from('scripts').select('id, created_at')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 810, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": "supabase.from('scripts').select('id, created_at')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 868, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 880, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 909, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 921, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 923, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('*')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 949, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 961, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { data, error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 990, "severity": "MEDIUM", "category": "Database Access", "description": "Database select operation", "code_snippet": ".select('id')", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 1002, "severity": "MEDIUM", "category": "Database Access", "description": "Direct table access", "code_snippet": "const { error } = await supabase", "recommendation": "Ensure proper RLS policies and avoid exposing sensitive database operations"}, {"file_path": "src/contexts/ThemeProvider.jsx", "line_number": 17, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "() => localStorage.getItem(storageKey) || defaultTheme", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/contexts/ThemeProvider.jsx", "line_number": 41, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "localStorage.setItem(storageKey, theme)", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/Home.jsx", "line_number": 21, "severity": "MEDIUM", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Failed to check admin status:', error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "src/pages/ScriptRequests.jsx", "line_number": 15, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "const storedRequests = JSON.parse(localStorage.getItem('scriptRequests') || '[]');", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "src/pages/ScriptRequests.jsx", "line_number": 28, "severity": "MEDIUM", "category": "Client-Side Security", "description": "localStorage usage", "code_snippet": "localStorage.setItem('scriptRequests', JSON.stringify(updatedRequests));", "recommendation": "Implement server-side validation for security-critical operations"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 8, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Missing Supabase environment variables');", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 20, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Supabase initialization error:', error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 185, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Unauthorized parent origin:', parent<PERSON><PERSON>in);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 188, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error saving changes: ${result.error}`);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 191, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error during fetch for ${editId}:`, error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/vite-plugin-react-inline-editor.js", "line_number": 206, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error transforming ${id}:`, error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "plugins/visual-editor/vite-plugin-react-inline-editor.js", "line_number": 305, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`[vite][visual-editor] Error during direct write for ${filePath}:`, writeError);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "src/components/ErrorBoundary.jsx", "line_number": 15, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(\"Uncaught error:\", error, errorInfo);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "src/pages/ScriptRequests.jsx", "line_number": 18, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(\"Failed to parse script requests from localStorage\", error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "src/pages/Scripts.jsx", "line_number": 74, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Error fetching scripts:', error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 147, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(`❌ Error processing ${filePath}:`, error.message);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 157, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Error: src/pages directory not found!');", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 158, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('Make sure you\\'re running this script from your project root.');", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "tools/generate-llms.js", "line_number": 169, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error('❌ No pages with Helmet components found!');", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "vite.config.js", "line_number": 129, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(\\`Fetch error from \\${requestUrl}: \\${errorFromRes}\\`);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "vite.config.js", "line_number": 136, "severity": "LOW", "category": "Console Lo<PERSON>", "description": "Console logging statement", "code_snippet": "console.error(error);", "recommendation": "Remove console logs in production or ensure no sensitive data is logged"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 4, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: SUPABASE_URL", "code_snippet": "const supabaseUrl = process.env.SUPABASE_URL;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 40, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: ALLOWED_ORIGIN", "code_snippet": "'Access-Control-Allow-Origin': process.env.ALLOWED_ORIGIN || 'https://senthelptakoisputtingmeonbasement.netlify.app',", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 126, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: NODE_ENV", "code_snippet": "nodeEnv: process.env.NODE_ENV", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 276, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: DISCORD_WEBHOOK_URL", "code_snippet": "const webhookUrl = process.env.DISCORD_WEBHOOK_URL;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "netlify/functions/getUserData.js", "line_number": 296, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "await fetch(webhookUrl, {", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "plugins/visual-editor/edit-mode-script.js", "line_number": 160, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "const response = await fetch(PLUGIN_APPLY_EDIT_API_URL, {", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "src/lib/apiClient.js", "line_number": 14, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "const response = await fetch(url, config);", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "src/lib/secureApiClient.js", "line_number": 25, "severity": "INFO", "category": "Network Requests", "description": "HTTP request", "code_snippet": "const response = await fetch(url, config);", "recommendation": "Ensure sensitive data is properly encrypted and validated server-side"}, {"file_path": "src/lib/supabaseClient.js", "line_number": 3, "severity": "INFO", "category": "Environment Variables", "description": "Vite environment variable access: VITE_SUPABASE_URL", "code_snippet": "const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}, {"file_path": "vite.config.js", "line_number": 5, "severity": "INFO", "category": "Environment Variables", "description": "Environment variable access: NODE_ENV", "code_snippet": "const isDev = process.env.NODE_ENV !== 'production';", "recommendation": "Ensure sensitive environment variables are not exposed to client-side"}]}