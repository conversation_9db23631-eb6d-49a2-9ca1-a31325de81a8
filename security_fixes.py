#!/usr/bin/env python3
"""
Bloxsync Security Quick Fixes
Automatically applies some basic security fixes to the codebase.
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Tuple

class SecurityFixer:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.fixes_applied = []
        
    def remove_console_logs(self, file_path: Path) -> bool:
        """Remove console.log statements from production code."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Remove console.log statements but keep console.error for important errors
            patterns_to_remove = [
                r'console\.log\s*\([^)]*\);\s*\n?',
                r'console\.warn\s*\([^)]*\);\s*\n?',
                r'console\.info\s*\([^)]*\);\s*\n?',
                r'console\.debug\s*\([^)]*\);\s*\n?'
            ]
            
            for pattern in patterns_to_remove:
                content = re.sub(pattern, '', content, flags=re.MULTILINE)
            
            # Clean up empty lines
            content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True
                
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            
        return False
    
    def add_security_headers(self) -> bool:
        """Add security headers to netlify.toml."""
        netlify_toml = self.project_root / 'netlify.toml'
        
        if not netlify_toml.exists():
            return False
            
        try:
            with open(netlify_toml, 'r') as f:
                content = f.read()
            
            # Check if security headers already exist
            if '[[headers]]' in content:
                return False
                
            security_headers = '''
# Security Headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co https://*.netlify.app;"
    Strict-Transport-Security = "max-age=31536000; includeSubDomains"
'''
            
            with open(netlify_toml, 'a') as f:
                f.write(security_headers)
                
            return True
            
        except Exception as e:
            print(f"Error updating netlify.toml: {e}")
            return False
    
    def create_security_middleware(self) -> bool:
        """Create a security middleware file for admin routes."""
        middleware_content = '''// Security middleware for admin routes
export const requireServerSideAuth = async (request) => {
  // This should be implemented server-side
  // Never trust client-side authentication
  
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response('Unauthorized', { status: 401 });
  }
  
  const token = authHeader.substring(7);
  
  // TODO: Implement JWT validation server-side
  // const isValid = await validateJWT(token);
  // if (!isValid) {
  //   return new Response('Invalid token', { status: 401 });
  // }
  
  return null; // Continue to route handler
};

// Rate limiting middleware
export const rateLimitMiddleware = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const requests = new Map();
  
  return (request) => {
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown';
    
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean old entries
    for (const [key, timestamps] of requests.entries()) {
      requests.set(key, timestamps.filter(time => time > windowStart));
      if (requests.get(key).length === 0) {
        requests.delete(key);
      }
    }
    
    // Check current IP
    const ipRequests = requests.get(ip) || [];
    if (ipRequests.length >= maxRequests) {
      return new Response('Rate limit exceeded', { status: 429 });
    }
    
    // Add current request
    ipRequests.push(now);
    requests.set(ip, ipRequests);
    
    return null; // Continue
  };
};
'''
        
        middleware_file = self.project_root / 'src' / 'lib' / 'security.js'
        
        try:
            with open(middleware_file, 'w') as f:
                f.write(middleware_content)
            return True
        except Exception as e:
            print(f"Error creating security middleware: {e}")
            return False
    
    def create_secure_api_client(self) -> bool:
        """Create a more secure API client with proper error handling."""
        secure_client_content = '''// Secure API Client with proper error handling
class SecureApiClient {
  constructor() {
    this.baseURL = '/.netlify/functions';
    this.timeout = 10000; // 10 second timeout
  }

  async request(action, params = {}, options = {}) {
    const url = `${this.baseURL}/getUserData`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);
    
    try {
      const config = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        body: JSON.stringify({ action, params }),
        signal: controller.signal
      };

      const response = await fetch(url, config);
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      return result;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      
      // Don't expose internal errors to client
      throw new Error('Request failed');
    }
  }

  // Specific methods with validation
  async trackUser(path) {
    if (!path || typeof path !== 'string') {
      throw new Error('Invalid path parameter');
    }
    return this.request('trackUser', { path });
  }

  async checkUserStatus() {
    return this.request('checkUserStatus', {});
  }

  // Admin methods should require server-side authentication
  async adminRequest(action, params = {}, authToken) {
    if (!authToken) {
      throw new Error('Authentication required');
    }
    
    return this.request(action, params, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
  }
}

export const secureApiClient = new SecureApiClient();
'''
        
        secure_client_file = self.project_root / 'src' / 'lib' / 'secureApiClient.js'
        
        try:
            with open(secure_client_file, 'w') as f:
                f.write(secure_client_content)
            return True
        except Exception as e:
            print(f"Error creating secure API client: {e}")
            return False
    
    def apply_fixes(self) -> None:
        """Apply all security fixes."""
        print("🔧 Applying security fixes to Bloxsync codebase...")
        print("=" * 50)
        
        # 1. Remove console.log statements
        print("1. Removing console.log statements...")
        js_files = list(self.project_root.rglob('*.js')) + list(self.project_root.rglob('*.jsx'))
        console_fixes = 0
        
        for file_path in js_files:
            if 'node_modules' not in str(file_path):
                if self.remove_console_logs(file_path):
                    console_fixes += 1
                    self.fixes_applied.append(f"Removed console logs from {file_path.relative_to(self.project_root)}")
        
        print(f"   ✅ Cleaned {console_fixes} files")
        
        # 2. Add security headers
        print("2. Adding security headers...")
        if self.add_security_headers():
            print("   ✅ Added security headers to netlify.toml")
            self.fixes_applied.append("Added security headers to netlify.toml")
        else:
            print("   ⚠️  Security headers already exist or netlify.toml not found")
        
        # 3. Create security middleware
        print("3. Creating security middleware...")
        if self.create_security_middleware():
            print("   ✅ Created src/lib/security.js")
            self.fixes_applied.append("Created security middleware")
        else:
            print("   ❌ Failed to create security middleware")
        
        # 4. Create secure API client
        print("4. Creating secure API client...")
        if self.create_secure_api_client():
            print("   ✅ Created src/lib/secureApiClient.js")
            self.fixes_applied.append("Created secure API client")
        else:
            print("   ❌ Failed to create secure API client")
        
        print(f"\n✅ Applied {len(self.fixes_applied)} security fixes!")
        
        # Print summary
        print("\n📋 Fixes Applied:")
        for fix in self.fixes_applied:
            print(f"   • {fix}")
        
        print("\n⚠️  Manual fixes still required:")
        print("   • Replace sessionStorage authentication with JWT tokens")
        print("   • Move admin operations to server-side functions")
        print("   • Remove direct Supabase client usage from admin components")
        print("   • Implement proper server-side validation")

def main():
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = '.'
    
    fixer = SecurityFixer(project_root)
    fixer.apply_fixes()

if __name__ == "__main__":
    main()
