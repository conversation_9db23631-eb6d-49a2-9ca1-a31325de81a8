
-- ============================================================================
-- 1. CREATE TABLES
-- ============================================================================

CREATE TABLE IF NOT EXISTS admin_keys (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    access_key TEXT NOT NULL UNIQUE,
    hwid TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    assigned_at TIMESTAMP WITH TIME ZONE,
    name TEXT DEFAULT 'Administrator'
);

CREATE TABLE IF NOT EXISTS script_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    game_name TEXT NOT NULL,
    game_link TEXT NOT NULL,
    script_description TEXT NOT NULL,
    discord_username TEXT,
    status TEXT DEFAULT 'Pending',
    hwid TEXT
);

CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    hwid TEXT NOT NULL UNIQUE,
    status TEXT DEFAULT 'neutral',
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS blacklisted_hwids (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    hwid TEXT NOT NULL UNIQUE,
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS page_visits (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    hwid TEXT,
    path TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS scripts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    game_name TEXT,
    code TEXT,
    category TEXT,
    status TEXT DEFAULT 'Online',
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_log JSONB DEFAULT '[]'::jsonb,
    image_prompt TEXT,
    place_id TEXT,
    universe_id TEXT,
    image_url TEXT
);

CREATE TABLE IF NOT EXISTS script_ratings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    script_id UUID REFERENCES scripts(id) ON DELETE CASCADE,
    hwid TEXT NOT NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(script_id, hwid)
);

-- ============================================================================
-- ENABLE ROW LEVEL SECURITY
-- ============================================================================

ALTER TABLE admin_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE script_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE blacklisted_hwids ENABLE ROW LEVEL SECURITY;
ALTER TABLE page_visits ENABLE ROW LEVEL SECURITY;
ALTER TABLE scripts ENABLE ROW LEVEL SECURITY;
ALTER TABLE script_ratings ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- DROP EXISTING POLICIES (Clean Slate)
-- ============================================================================

DROP POLICY IF EXISTS "Block all access to admin_keys" ON admin_keys;

-- Script Requests
DROP POLICY IF EXISTS "Allow public script request inserts" ON script_requests;
DROP POLICY IF EXISTS "Allow anon script submissions" ON script_requests;
DROP POLICY IF EXISTS "Allow authenticated script submissions" ON script_requests;
DROP POLICY IF EXISTS "Block script request reads" ON script_requests;
DROP POLICY IF EXISTS "Block script request updates" ON script_requests;
DROP POLICY IF EXISTS "Block script request deletes" ON script_requests;

-- Users
DROP POLICY IF EXISTS "Allow public user reads" ON users;
DROP POLICY IF EXISTS "Allow public user creation" ON users;
DROP POLICY IF EXISTS "Block public user updates" ON users;
DROP POLICY IF EXISTS "Block public user deletes" ON users;
DROP POLICY IF EXISTS "Block all access to users" ON users;

-- Blacklisted HWIDs
DROP POLICY IF EXISTS "Block all access to blacklisted_hwids" ON blacklisted_hwids;

-- Page Visits
DROP POLICY IF EXISTS "Allow public page visit reads" ON page_visits;
DROP POLICY IF EXISTS "Allow public page visit inserts" ON page_visits;
DROP POLICY IF EXISTS "Allow public page visits" ON page_visits;
DROP POLICY IF EXISTS "Block reading page visits" ON page_visits;
DROP POLICY IF EXISTS "Block page visit updates" ON page_visits;
DROP POLICY IF EXISTS "Block page visit deletes" ON page_visits;

-- Scripts
DROP POLICY IF EXISTS "Allow public script reading" ON scripts;
DROP POLICY IF EXISTS "Block public script inserts" ON scripts;
DROP POLICY IF EXISTS "Block public script updates" ON scripts;
DROP POLICY IF EXISTS "Block public script deletes" ON scripts;
DROP POLICY IF EXISTS "Block public script modifications" ON scripts;

-- Script Ratings
DROP POLICY IF EXISTS "Allow public script ratings read" ON script_ratings;
DROP POLICY IF EXISTS "Allow public script ratings insert" ON script_ratings;
DROP POLICY IF EXISTS "Allow public script ratings updates" ON script_ratings;
DROP POLICY IF EXISTS "Block script ratings modifications" ON script_ratings;
DROP POLICY IF EXISTS "Block script ratings deletions" ON script_ratings;

-- ============================================================================
-- 4. CREATE NEW SECURITY POLICIES
-- ============================================================================

-- ADMIN KEYS - Block all public access (admin only)
CREATE POLICY "Block all access to admin_keys" ON admin_keys
    FOR ALL
    USING (false)
    WITH CHECK (false);

-- SCRIPT REQUESTS - Allow public submissions, block everything else
CREATE POLICY "Allow public script request inserts" ON script_requests
    FOR INSERT
    WITH CHECK (true);

CREATE POLICY "Block script request reads" ON script_requests
    FOR SELECT
    USING (false);

CREATE POLICY "Block script request updates" ON script_requests
    FOR UPDATE
    USING (false)
    WITH CHECK (false);

CREATE POLICY "Block script request deletes" ON script_requests
    FOR DELETE
    USING (false);

-- USERS - Allow reads and inserts for user tracking
CREATE POLICY "Allow public user reads" ON users
    FOR SELECT
    USING (true);

CREATE POLICY "Allow public user creation" ON users
    FOR INSERT
    WITH CHECK (true);

CREATE POLICY "Block public user updates" ON users
    FOR UPDATE
    USING (false)
    WITH CHECK (false);

CREATE POLICY "Block public user deletes" ON users
    FOR DELETE
    USING (false);

-- BLACKLISTED HWIDS - Block all public access (admin only)
CREATE POLICY "Block all access to blacklisted_hwids" ON blacklisted_hwids
    FOR ALL
    USING (false)
    WITH CHECK (false);

-- PAGE VISITS - Allow reads and inserts for tracking
CREATE POLICY "Allow public page visit reads" ON page_visits
    FOR SELECT
    USING (true);

CREATE POLICY "Allow public page visit inserts" ON page_visits
    FOR INSERT
    WITH CHECK (true);

CREATE POLICY "Block page visit updates" ON page_visits
    FOR UPDATE
    USING (false)
    WITH CHECK (false);

CREATE POLICY "Block page visit deletes" ON page_visits
    FOR DELETE
    USING (false);

-- SCRIPTS - Allow public reading, block modifications
CREATE POLICY "Allow public script reading" ON scripts
    FOR SELECT
    USING (true);

CREATE POLICY "Block public script inserts" ON scripts
    FOR INSERT
    WITH CHECK (false);

CREATE POLICY "Block public script updates" ON scripts
    FOR UPDATE
    USING (false)
    WITH CHECK (false);

CREATE POLICY "Block public script deletes" ON scripts
    FOR DELETE
    USING (false);

-- SCRIPT RATINGS - Allow public read/write for rating system
CREATE POLICY "Allow public script ratings read" ON script_ratings
    FOR SELECT
    USING (true);

CREATE POLICY "Allow public script ratings insert" ON script_ratings
    FOR INSERT
    WITH CHECK (true);

CREATE POLICY "Allow public script ratings updates" ON script_ratings
    FOR UPDATE
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Block script ratings deletions" ON script_ratings
    FOR DELETE
    USING (false);

-- ============================================================================
-- VERIFICATION QUERY
-- ============================================================================

SELECT
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies
WHERE tablename IN (
    'admin_keys',
    'script_requests',
    'users',
    'blacklisted_hwids',
    'page_visits',
    'scripts',
    'script_ratings'
)
ORDER BY tablename, policyname;

ALTER TABLE scripts
ADD COLUMN IF NOT EXISTS category TEXT,
ADD COLUMN IF NOT EXISTS image_prompt TEXT,
ADD COLUMN IF NOT EXISTS place_id TEXT,
ADD COLUMN IF NOT EXISTS universe_id TEXT,
ADD COLUMN IF NOT EXISTS image_url TEXT;

-- Add HWID column to script_requests for spam tracking
ALTER TABLE script_requests
ADD COLUMN IF NOT EXISTS hwid TEXT;

-- ============================================================================
-- SAMPLE DATA 
-- ============================================================================

-- test data
INSERT INTO scripts (title, description, game_name, code, category, status, update_log)
VALUES (
    'Sample Script',
    'This is a sample script for testing purposes.',
    'Test Game',
    'print("Hello World!")',
    'utility',
    'Online',
    '[{"version": "1.0", "date": "2024-01-01", "changes": ["Initial release"]}]'::jsonb
) ON CONFLICT DO NOTHING;

-- ============================================================================
-- REMOVE INSECURE RPC FUNCTIONS (if they exist)
-- ============================================================================

DROP FUNCTION IF EXISTS is_admin_hwid(TEXT);

-- add admin keys
INSERT INTO admin_keys (access_key, hwid, name) 
VALUES ('password', 'hwid-if-no-hwid-leave-it-empty-it-will-fill-it', 'PRO');

INSERT INTO admin_keys (access_key, hwid, name) 
VALUES ('TakoDaMonkey001', 'f26262fa-90cf-4141-8a7e-999a633e3ff4', 'Noob');

-- this is how you add a blacklisted user
INSERT INTO blacklisted_hwids (hwid, reason) 
VALUES ('5731e25b85495fdb9be1b7eeb9e12aaac04610d7499732d3e96ccae45a07cd5a', 'Testing blacklist system');

-- this is how you remove a blacklisted user
DELETE FROM blacklisted_hwids 
WHERE hwid = '5731e25b85495fdb9be1b7eeb9e12aaac04610d7499732d3e96ccae45a07cd5a';

-- if you dont see this you are cooked 
SELECT 'Database setup completed successfully!' as status;
