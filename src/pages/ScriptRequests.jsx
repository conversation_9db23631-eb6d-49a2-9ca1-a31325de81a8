import React, { useState, useEffect } from 'react';
import { He<PERSON><PERSON> } from 'react-helmet';
import PageWrapper from '@/components/PageWrapper';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Info, Hourglass, CheckCircle2, XCircle, Database, Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { apiClient } from '@/lib/apiClient';
import { useToast } from '@/components/ui/use-toast';

const ScriptRequests = () => {
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchRequests = async () => {
      try {
        const result = await apiClient.request('getPublicScriptRequests');
        setRequests(result.data || []);
      } catch (error) {
        toast({
          title: "Error",
          description: "Could not fetch script requests.",
          variant: "destructive"
        });
        setRequests([]);
      } finally {
        setLoading(false);
      }
    };

    fetchRequests();
  }, [toast]);

  // This page is now read-only for public users
  // Status changes are handled by admins in the admin panel

  const getStatusInfo = (status) => {
    switch (status) {
      case 'Accepted':
        return {
          icon: <CheckCircle2 className="w-5 h-5 text-green-500" />,
          borderColor: 'border-l-green-500',
          textColor: 'text-green-500',
        };
      case 'Denied':
        return {
          icon: <XCircle className="w-5 h-5 text-red-500" />,
          borderColor: 'border-l-red-500',
          textColor: 'text-red-500',
        };
      default: // Pending
        return {
          icon: <Hourglass className="w-5 h-5 text-yellow-500 animate-spin" />,
          borderColor: 'border-l-yellow-500',
          textColor: 'text-yellow-500',
        };
    }
  };

  return (
    <>
      <Helmet>
        <title>Script Requests - 6FootScripts</title>
        <meta name="description" content="View the status of community-submitted script requests." />
      </Helmet>
      <PageWrapper
        title={<>Script Request <span className="bg-gradient-to-r from-primary to-pink-500 bg-clip-text text-transparent">Tracker</span></>}
        description="Here you can see the list of submitted script requests and their current status."
      >
        <Alert className="mb-8 bg-blue-900/10 border-blue-500/30 text-blue-foreground">
          <Database className="h-4 w-4 !text-blue-500" />
          <AlertTitle className="text-blue-400">Script Request Tracker</AlertTitle>
          <AlertDescription className="text-blue-400/80">
            View all community script requests and their current status. Status updates are managed by administrators.
          </AlertDescription>
        </Alert>

        {loading ? (
          <div className="text-center py-16">
            <Loader2 className="w-16 h-16 text-muted-foreground/40 mx-auto mb-4 animate-spin" />
            <h3 className="text-2xl font-bold mb-2">Loading Requests</h3>
            <p className="text-muted-foreground">Fetching script requests from server...</p>
          </div>
        ) : requests.length === 0 ? (
          <div className="text-center py-16">
            <Info className="w-16 h-16 text-muted-foreground/40 mx-auto mb-4" />
            <h3 className="text-2xl font-bold mb-2">No Requests Yet</h3>
            <p className="text-muted-foreground">Looks like no one has requested a script. Be the first!</p>
          </div>
        ) : (
          <div className="space-y-6">
            {requests.map((request, index) => {
              const statusInfo = getStatusInfo(request.status);
              return (
                <motion.div
                  key={request.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card className={`overflow-hidden border-l-4 ${statusInfo.borderColor}`}>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle>{request.game_name}</CardTitle>
                          <CardDescription>Requested on {new Date(request.created_at).toLocaleDateString()}</CardDescription>
                        </div>
                        <div className={`flex items-center gap-2 font-semibold ${statusInfo.textColor}`}>
                          {statusInfo.icon}
                          <span>{request.status}</span>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">{request.script_description}</p>
                      {request.discord_username && (
                         <p className="text-xs text-muted-foreground mt-4">Requester: {request.discord_username}</p>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        )}
      </PageWrapper>
    </>
  );
};

export default ScriptRequests;