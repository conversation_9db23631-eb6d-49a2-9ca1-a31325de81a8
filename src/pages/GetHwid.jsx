
import React from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { getHwid } from '@/lib/hwid';
import { Copy, Fingerprint } from 'lucide-react';
import PageWrapper from '@/components/PageWrapper';

const GetHwid = () => {
  const { toast } = useToast();
  const [hwid, setHwid] = React.useState('');
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    const fetchHwid = async () => {
      try {
        const id = await getHwid();
        setHwid(id);
      } catch (error) {
        toast({ title: "Error", description: "Could not generate HWID.", variant: "destructive" });
      } finally {
        setLoading(false);
      }
    };
    fetchHwid();
  }, [toast]);

  const handleCopy = () => {
    navigator.clipboard.writeText(hwid).then(() => {
      toast({ title: "Copied!", description: "Your HWID has been copied to the clipboard." });
    }).catch(() => {
      toast({ title: "Error", description: "Could not copy HWID.", variant: "destructive" });
    });
  };

  return (
    <>
      <Helmet>
        <title>Get Your HWID - 6FootScripts</title>
        <meta name="description" content="Find and copy your unique Hardware ID (HWID)." />
      </Helmet>
      <PageWrapper>
        <div className="flex items-center justify-center py-12 px-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="w-full max-w-2xl"
          >
            <Card>
              <CardHeader className="text-center">
                <Fingerprint className="mx-auto h-12 w-12 text-primary" />
                <CardTitle className="mt-4">Your Hardware ID (HWID)</CardTitle>
                <CardDescription>
                  This is your unique device identifier. You may need this to get access to certain features.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-center text-sm text-muted-foreground mb-6">
                  Your HWID is securely generated server-side from your IP address. It helps us identify your device without collecting personal information.
                </p>
                <div className="flex w-full items-center space-x-2">
                  <Input
                    readOnly
                    value={loading ? "Loading..." : hwid}
                    className="font-mono text-center"
                    disabled={loading}
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleCopy}
                    disabled={loading || !hwid}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </PageWrapper>
    </>
  );
};

export default GetHwid;
