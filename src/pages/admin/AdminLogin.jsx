
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Shield, KeyRound, Loader2 } from 'lucide-react';
import { useAdminAuth } from '@/contexts/AdminAuthContext';

const AdminLogin = () => {
  const [accessKey, setAccessKey] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { login } = useAdminAuth();

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (!accessKey) {
        toast({ variant: 'destructive', title: 'Login Failed', description: 'Please enter an access key.' });
        setLoading(false);
        return;
      }

      const result = await login(accessKey);

      if (result.success) {
        toast({ title: 'Login Successful', description: 'Redirecting to Admin Panel...' });

        setTimeout(() => {
          navigate('/admin/main_panel', { replace: true });
        }, 1000);
      } else {
        toast({ variant: 'destructive', title: 'Login Failed', description: result.error || 'An unknown error occurred.' });
        setLoading(false);
      }

    } catch (error) {
      toast({
        variant: "destructive",
        title: "Login Error",
        description: error.message || "An unexpected error occurred. Please try again."
      });
      setLoading(false);
    }
  };

  return (
    <>
      <Helmet>
        <title>Admin Login - 6FootScripts</title>
      </Helmet>
      <div className="min-h-screen flex items-center justify-center main-bg p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <Shield className="mx-auto h-12 w-12 text-primary" />
              <CardTitle className="mt-4">Admin Panel Login</CardTitle>
              <CardDescription>Enter your access key to access the dashboard.</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleLogin} className="space-y-6">
                 <div className="space-y-2">
                  <Label htmlFor="key" className="flex items-center">
                    <KeyRound className="w-4 h-4 mr-2" />
                    Access Key
                  </Label>
                  <Input
                    id="key"
                    type="password"
                    value={accessKey}
                    onChange={(e) => setAccessKey(e.target.value)}
                    required
                    disabled={loading}
                    autoComplete="current-password"
                  />
                </div>
                <Button type="submit" className="w-full" disabled={loading}>
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Login
                </Button>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </>
  );
};

export default AdminLogin;
