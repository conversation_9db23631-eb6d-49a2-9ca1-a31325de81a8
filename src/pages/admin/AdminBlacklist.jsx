import React, { useState, useEffect, useCallback } from 'react';
import { Helmet } from 'react-helmet';
import PageWrapper from '@/components/PageWrapper';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, UserX, Trash2 } from 'lucide-react';
import { apiClient } from '@/lib/apiClient';
import { useToast } from '@/components/ui/use-toast';

const AdminBlacklist = () => {
  const [blacklist, setBlacklist] = useState([]);
  const [loading, setLoading] = useState(true);
  const [newHwid, setNewHwid] = useState('');
  const [newReason, setNewReason] = useState('');
  const [isAdding, setIsAdding] = useState(false);
  const { toast } = useToast();

  const fetchBlacklist = useCallback(async () => {
    setLoading(true);
    try {
      const result = await apiClient.request('getBlacklistedHwids');
      setBlacklist(result.data || []);
    } catch (error) {
      toast({ title: 'Error fetching blacklist', description: error.message, variant: 'destructive' });
      setBlacklist([]);
    }
    setLoading(false);
  }, [toast]);

  useEffect(() => {
    fetchBlacklist();
  }, [fetchBlacklist]);

  const handleAddHwid = async (e) => {
    e.preventDefault();
    if (!newHwid) {
      toast({ title: 'HWID cannot be empty', variant: 'destructive' });
      return;
    }
    setIsAdding(true);
    try {
      await apiClient.request('addBlacklistedHwid', { hwid: newHwid, reason: newReason });
      toast({ title: 'Success!', description: 'HWID has been blacklisted.' });
      setNewHwid('');
      setNewReason('');
      fetchBlacklist();
    } catch (error) {
      toast({ title: 'Error adding HWID', description: error.message, variant: 'destructive' });
    }
    setIsAdding(false);
  };

  const handleDeleteHwid = async (id) => {
    try {
      await apiClient.request('removeBlacklistedHwid', { id });
      toast({ title: 'Success!', description: 'HWID has been removed from the blacklist.' });
      fetchBlacklist();
    } catch (error) {
      toast({ title: 'Error removing HWID', description: error.message, variant: 'destructive' });
    }
  };

  return (
    <>
      <Helmet>
        <title>Blacklist Manager - 6FootScripts Admin</title>
      </Helmet>
      <PageWrapper title="Blacklist Manager" description="Manage user access by blacklisting HWIDs.">
        <Card>
          <CardHeader>
            <CardTitle>Add to Blacklist</CardTitle>
            <CardDescription>Enter a HWID and an optional reason to block a user.</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleAddHwid} className="space-y-4">
              <Input
                placeholder="Enter HWID to blacklist"
                value={newHwid}
                onChange={(e) => setNewHwid(e.target.value)}
              />
              <Textarea
                placeholder="Reason for blacklist (optional)"
                value={newReason}
                onChange={(e) => setNewReason(e.target.value)}
              />
              <Button type="submit" disabled={isAdding}>
                {isAdding ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <UserX className="w-4 h-4 mr-2" />}
                Add HWID
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Blacklisted HWIDs</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center py-8"><Loader2 className="animate-spin h-8 w-8 text-primary" /></div>
            ) : (
              <div className="rounded-md border">
                <div className="max-h-[50vh] overflow-y-auto">
                  {blacklist.map(item => (
                    <div key={item.id} className="flex justify-between items-center p-3 border-b last:border-b-0">
                      <div>
                        <p className="font-mono text-sm">{item.hwid}</p>
                        <p className="text-xs text-muted-foreground">{item.reason || 'No reason provided'}</p>
                      </div>
                      <Button variant="destructive" size="icon" onClick={() => handleDeleteHwid(item.id)}>
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </PageWrapper>
    </>
  );
};

export default AdminBlacklist;