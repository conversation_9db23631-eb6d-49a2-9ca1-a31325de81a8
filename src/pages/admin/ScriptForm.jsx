
import React, { useState, useEffect } from 'react';
import { apiClient } from '@/lib/apiClient';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';
import { DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const ScriptForm = ({ script, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    code: '',
    category: '',
    status: 'Online',
    last_updated: new Date().toISOString().split('T')[0],
    update_log: '[]',
    image_prompt: '',
    place_id: '',
    universe_id: '',
    image_url: '',
  });
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (script) {
      setFormData({
        title: script.title || '',
        description: script.description || '',
        code: script.code || '',
        category: script.category || '',
        status: script.status || 'Online',
        last_updated: new Date(script.last_updated || new Date()).toISOString().split('T')[0],
        update_log: JSON.stringify(script.update_log || [], null, 2),
        image_prompt: script.image_prompt || '',
        place_id: script.place_id || '',
        universe_id: script.universe_id || '',
        image_url: script.image_url || '',
      });
    } else {
        setFormData({
            title: '', description: '', code: '', category: '', status: 'Online',
            last_updated: new Date().toISOString().split('T')[0],
            update_log: '[]', image_prompt: '', place_id: '', universe_id: '', image_url: ''
        });
    }
  }, [script]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleStatusChange = (value) => {
    setFormData(prev => ({...prev, status: value}));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    let parsedLog;
    try {
        parsedLog = JSON.parse(formData.update_log);
    } catch (error) {
        toast({ title: 'Invalid JSON', description: 'Update Log is not valid JSON.', variant: 'destructive' });
        setLoading(false);
        return;
    }
    
    const dataToSubmit = { ...formData, update_log: parsedLog };

    if (dataToSubmit.place_id) {
        // TODO: Implement secure Roblox thumbnail API
        toast({ title: 'Thumbnail Fetch', description: 'Thumbnail fetching temporarily disabled.', variant: 'default' });
    }


    try {
      if (script) {
        await apiClient.updateScript(script.id, dataToSubmit);
      } else {
        await apiClient.createScript(dataToSubmit);
      }

      toast({ title: 'Success!', description: `Script ${script ? 'updated' : 'created'} successfully.` });
      onSave();
    } catch (error) {
      toast({ title: 'Error saving script', description: 'Failed to save script', variant: 'destructive' });
    }
    setLoading(false);
  };

  return (
    <>
      <DialogHeader>
        <DialogTitle>{script ? 'Edit Script' : 'Add New Script'}</DialogTitle>
        <DialogDescription>
          {script ? 'Update the details for this script.' : 'Fill in the details for the new script.'}
        </DialogDescription>
      </DialogHeader>
      <form onSubmit={handleSubmit} className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto px-1">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="title" className="text-right">Title</Label>
          <Input id="title" name="title" value={formData.title} onChange={handleChange} className="col-span-3" required />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="description" className="text-right">Description</Label>
          <Textarea id="description" name="description" value={formData.description} onChange={handleChange} className="col-span-3" required />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="code" className="text-right">Loadstring</Label>
          <Textarea id="code" name="code" value={formData.code} onChange={handleChange} className="col-span-3 font-mono" required />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="place_id" className="text-right">Place ID</Label>
          <Input id="place_id" name="place_id" value={formData.place_id} onChange={handleChange} className="col-span-3" placeholder="e.g., 8737899170" />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="category" className="text-right">Category</Label>
          <Input id="category" name="category" value={formData.category} onChange={handleChange} className="col-span-3" />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="status" className="text-right">Status</Label>
            <Select onValueChange={handleStatusChange} value={formData.status}>
                <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="Online">Online</SelectItem>
                    <SelectItem value="Discontinued">Discontinued</SelectItem>
                    <SelectItem value="Coming Soon">Coming Soon</SelectItem>
                </SelectContent>
            </Select>
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="last_updated" className="text-right">Last Updated</Label>
          <Input id="last_updated" name="last_updated" type="date" value={formData.last_updated} onChange={handleChange} className="col-span-3" required />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="update_log" className="text-right">Update Log (JSON)</Label>
          <Textarea id="update_log" name="update_log" value={formData.update_log} onChange={handleChange} className="col-span-3 font-mono" rows={5} />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="image_prompt" className="text-right">Image Prompt</Label>
          <Input id="image_prompt" name="image_prompt" value={formData.image_prompt} onChange={handleChange} className="col-span-3" placeholder="Fallback if Place ID fails" />
        </div>
      </form>
      <DialogFooter>
        <Button variant="outline" onClick={onCancel}>Cancel</Button>
        <Button type="submit" onClick={handleSubmit} disabled={loading}>
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Save Script
        </Button>
      </DialogFooter>
    </>
  );
};

export default ScriptForm;
