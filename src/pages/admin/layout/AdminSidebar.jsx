
import React from 'react';
import { NavLink, Link, useNavigate } from 'react-router-dom';
import { LayoutDashboard, KeyRound, FileText, Code, ShieldOff, LogOut, Code2, UserCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useAdminAuth } from '@/contexts/AdminAuthContext';

const navItems = [
  { to: '/admin/dashboard', icon: LayoutDashboard, label: 'Dashboard' },
  { to: '/admin/keys', icon: KeyRound, label: 'Keys' },
  { to: '/admin/requests', icon: FileText, label: 'Requests' },
  { to: '/admin/scripts', icon: Code, label: 'Scripts' },
  { to: '/admin/blacklist', icon: ShieldOff, label: 'Blacklist' },
];

const AdminSidebar = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { adminName, logout } = useAdminAuth();

  const handleLogout = () => {
    logout();
    toast({ title: 'Logged Out', description: 'You have been successfully logged out.' });
    navigate('/admin/login');
  };

  return (
    <aside className="w-64 flex-shrink-0 bg-card border-r flex flex-col">
      <div className="h-20 border-b flex items-center px-6">
        <Link to="/" className="flex items-center gap-2">
          <Code2 className="w-8 h-8 text-primary" />
          <span className="text-xl font-bold">6FootAdmin</span>
        </Link>
      </div>
      <nav className="flex-grow px-4 py-6">
        <ul className="space-y-2">
          {navItems.map(item => (
            <li key={item.to}>
              <NavLink
                to={item.to}
                className={({ isActive }) =>
                  `flex items-center gap-3 px-4 py-2.5 rounded-lg text-sm font-medium transition-colors ${
                    isActive
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                  }`
                }
              >
                <item.icon className="w-5 h-5" />
                <span>{item.label}</span>
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>
      <div className="p-4 border-t space-y-4">
        <div className="flex items-center gap-3 px-2 py-1 rounded-lg">
          <UserCircle className="w-8 h-8 text-muted-foreground" />
          <div>
            <p className="text-sm font-semibold">{adminName || 'Administrator'}</p>
            <p className="text-xs text-muted-foreground">Online</p>
          </div>
        </div>
        <Button variant="ghost" className="w-full justify-start" onClick={handleLogout}>
          <LogOut className="w-5 h-5 mr-3" />
          Logout
        </Button>
      </div>
    </aside>
  );
};

export default AdminSidebar;
