
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { apiClient } from '@/lib/apiClient';

const AdminAuthContext = createContext({
  isAdmin: false,
  adminName: null,
  loading: true,
  login: async () => {},
  logout: () => {},
  checkStatus: async () => {},
});

export const useAdminAuth = () => useContext(AdminAuthContext);

export const AdminAuthProvider = ({ children }) => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [adminName, setAdminName] = useState(null);
  const [loading, setLoading] = useState(true);

  const checkAdminStatus = useCallback(async () => {
    setLoading(true);
    try {
      // Server-side admin status check
      const result = await apiClient.checkAdminStatus();
      setIsAdmin(result.isAdmin);
      setAdminName(result.name || null);
    } catch (error) {
      setIsAdmin(false);
      setAdminName(null);
    } finally {
      setLoading(false);
    }
  }, []);

  const login = async (accessKey) => {
    try {
      const result = await apiClient.request('adminLogin', { accessKey });
      if (result.success) {
        setIsAdmin(true);
        setAdminName(result.name);
        return { success: true };
      }
      return { success: false, error: result.error };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const logout = () => {
    setIsAdmin(false);
    setAdminName(null);
  };

  useEffect(() => {
    checkAdminStatus();
  }, [checkAdminStatus]);

  const value = {
    isAdmin,
    adminName,
    loading,
    login,
    logout,
    checkStatus: checkAdminStatus
  };

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  );
};
