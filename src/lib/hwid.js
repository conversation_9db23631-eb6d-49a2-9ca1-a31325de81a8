// Secure HWID system - Server-side only
// HWID is now generated server-side from IP address for security

import { apiClient } from './apiClient.js';

/**
 * Get HWID from server - no client-side storage or generation
 * This is secure because HWID is generated server-side from IP address
 */
export const getHwid = async () => {
  try {
    const result = await apiClient.request('getMyHwid');
    return result.hwid;
  } catch (error) {
    throw new Error('Failed to get HWID from server');
  }
};

/**
 * Legacy function for compatibility - now redirects to server
 * @deprecated Use getHwid() instead
 */
export const getClientHwid = getHwid;