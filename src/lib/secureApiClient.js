// Secure API Client with proper error handling
class SecureApiClient {
  constructor() {
    this.baseURL = '/.netlify/functions';
    this.timeout = 10000; // 10 second timeout
  }

  async request(action, params = {}, options = {}) {
    const url = `${this.baseURL}/getUserData`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);
    
    try {
      const config = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        body: JSON.stringify({ action, params }),
        signal: controller.signal
      };

      const response = await fetch(url, config);
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      return result;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      
      // Don't expose internal errors to client
      throw new Error('Request failed');
    }
  }

  // Specific methods with validation
  async trackUser(path) {
    if (!path || typeof path !== 'string') {
      throw new Error('Invalid path parameter');
    }
    return this.request('trackUser', { path });
  }

  async checkUserStatus() {
    return this.request('checkUserStatus', {});
  }

  // Admin methods should require server-side authentication
  async adminRequest(action, params = {}, authToken) {
    if (!authToken) {
      throw new Error('Authentication required');
    }
    
    return this.request(action, params, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
  }
}

export const secureApiClient = new SecureApiClient();
