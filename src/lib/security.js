// Security middleware for admin routes
export const requireServerSideAuth = async (request) => {
  // This should be implemented server-side
  // Never trust client-side authentication
  
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response('Unauthorized', { status: 401 });
  }
  
  const token = authHeader.substring(7);
  
  // TODO: Implement JWT validation server-side
  // const isValid = await validateJWT(token);
  // if (!isValid) {
  //   return new Response('Invalid token', { status: 401 });
  // }
  
  return null; // Continue to route handler
};

// Rate limiting middleware
export const rateLimitMiddleware = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const requests = new Map();
  
  return (request) => {
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown';
    
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean old entries
    for (const [key, timestamps] of requests.entries()) {
      requests.set(key, timestamps.filter(time => time > windowStart));
      if (requests.get(key).length === 0) {
        requests.delete(key);
      }
    }
    
    // Check current IP
    const ipRequests = requests.get(ip) || [];
    if (ipRequests.length >= maxRequests) {
      return new Response('Rate limit exceeded', { status: 429 });
    }
    
    // Add current request
    ipRequests.push(now);
    requests.set(ip, ipRequests);
    
    return null; // Continue
  };
};
