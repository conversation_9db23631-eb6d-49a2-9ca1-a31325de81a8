
import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { apiClient } from '@/lib/apiClient';
import GlobalLoader from '@/components/GlobalLoader';

const AdminAccessGuard = ({ children }) => {
  const [isAuthorized, setIsAuthorized] = useState(null);
  const location = useLocation();

  useEffect(() => {
    const checkAuthorization = async () => {
      // Always allow access to login page
      if (location.pathname === '/admin/login') {
        setIsAuthorized(true);
        return;
      }

      try {
        // Server-side admin status check - no client-side storage
        const result = await apiClient.checkAdminStatus();
        setIsAuthorized(result.isAdmin);
      } catch (error) {
        // Don't log sensitive errors in production
        setIsAuthorized(false);
      }
    };

    checkAuthorization();
  }, [location.pathname]);

  if (isAuthorized === null) {
    return <GlobalLoader />;
  }

  // Redirect non-admin users to home
  if (!isAuthorized) {
    return <Navigate to="/" replace />;
  }

  return children;
};

export default AdminAccessGuard;
