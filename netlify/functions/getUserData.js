const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
}

let supabase;
if (supabaseUrl && supabaseKey) {
  try {
    supabase = createClient(supabaseUrl, supabaseKey, {
      auth: {
        persistSession: false
      }
    });
  } catch (error) {
    console.error('Supabase initialization error:', error);
  }
}

const generateHwidFromIP = (ip) => {
  return crypto.createHash('sha256').update(ip).digest('hex');
};

const getClientIP = (event) => {
  const ip = event.headers['x-forwarded-for'] ||
            event.headers['x-real-ip'] ||
            event.headers['cf-connecting-ip'] ||
            event.headers['x-client-ip'] ||
            event.connection?.remoteAddress ||
            '127.0.0.1';

  return ip.split(',')[0].trim();
};



const corsHeaders = {
  'Access-Control-Allow-Origin': process.env.ALLOWED_ORIGIN || 'https://senthelptakoisputtingmeonbasement.netlify.app',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

exports.handler = async (event) => {
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }



  try {

    const { action, params } = JSON.parse(event.body || '{}');

    switch (action) {
      case 'getScripts':
        return await getScripts();

      case 'rateScript':
        return await rateScript(params, event);

      case 'getUserRatings':
        return await getUserRatings(params, event);

      case 'submitScriptRequest':
        return await submitScriptRequest(params, event);

      case 'trackUser':
        return await trackUser(params, event);

      case 'checkUserStatus':
        return await checkUserStatus(params, event);

      case 'checkAdminStatus':
        return await checkAdminStatus(params, event);

      case 'adminLogin':
        return await adminLogin(params, event);

      case 'getAdminKeys':
        return await getAdminKeys(params, event);

      case 'getScriptRequests':
        return await getScriptRequests(params, event);

      case 'updateRequestStatus':
        return await updateRequestStatus(params, event);

      case 'createScript':
        return await createScript(params, event);

      case 'updateScript':
        return await updateScript(params, event);

      case 'deleteScript':
        return await deleteScript(params, event);

      case 'getAdminStats':
        return await getAdminStats(params, event);

      case 'test':
        return {
          statusCode: 200,
          headers: corsHeaders,
          body: JSON.stringify({
            message: 'API is working!',
            timestamp: new Date().toISOString(),
            env: {
              hasUrl: !!supabaseUrl,
              hasKey: !!supabaseKey,
              nodeEnv: process.env.NODE_ENV
            }
          })
        };

      case 'getMyHwid':
        const clientIP = getClientIP(event);
        const myHwid = generateHwidFromIP(clientIP);
        return {
          statusCode: 200,
          headers: corsHeaders,
          body: JSON.stringify({
            hwid: myHwid,
            message: 'This is your server-generated HWID'
          })
        };

      default:
        return {
          statusCode: 400,
          headers: corsHeaders,
          body: JSON.stringify({ error: `Invalid action: ${action}` })
        };
    }
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Internal server error'
      })
    };
  }
};

async function getScripts() {
  try {
    const { data, error } = await supabase
      .from('scripts')
      .select(`*, script_ratings ( rating )`);

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function rateScript({ scriptId, rating }, event) {
  try {
    if (!scriptId || rating === undefined) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }

    const clientIP = getClientIP(event);
    const hwid = generateHwidFromIP(clientIP);

    const { data, error } = await supabase
      .from('script_ratings')
      .upsert({
        script_id: scriptId,
        hwid: hwid,
        rating: rating
      }, {
        onConflict: 'script_id,hwid'
      });

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function getUserRatings(_, event) {
  try {
    const clientIP = getClientIP(event);
    const hwid = generateHwidFromIP(clientIP);

    const { data, error } = await supabase
      .from('script_ratings')
      .select('script_id, rating')
      .eq('hwid', hwid);

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function submitScriptRequest({ gameName, gameLink, scriptDescription, discordUsername }, event) {
  try {
    if (!gameName || !scriptDescription) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required fields' })
      };
    }

    const clientIP = getClientIP(event);
    const serverGeneratedHwid = generateHwidFromIP(clientIP);

    const { data, error } = await supabase
      .from('script_requests')
      .insert({
        game_name: gameName,
        game_link: gameLink,
        script_description: scriptDescription,
        discord_username: discordUsername || null,
        status: 'Pending',
        hwid: serverGeneratedHwid
      })
      .select()
      .single();

    if (error) throw error;

    const webhookUrl = process.env.DISCORD_WEBHOOK_URL;
    if (webhookUrl) {
      try {
        const embed = {
          title: 'New Script Request',
          color: 8535234,
          fields: [
            { name: 'Game Name', value: gameName, inline: true },
            { name: 'Discord Username', value: discordUsername || 'Not Provided', inline: true },
            { name: 'Status', value: '🕒 Pending', inline: true },
            { name: 'Game Link', value: `[Click Here](${gameLink})` },
            { name: 'Script Description', value: scriptDescription },
            { name: 'Request ID', value: `\`${data.id}\``, inline: false}
          ],
          footer: { text: '6FootScripts Request System' },
          timestamp: new Date().toISOString(),
        };

        const payload = { embeds: [embed] };

        await fetch(webhookUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });
      } catch (discordError) {
        // Discord notification failed silently
      }
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function trackUser({ path }, event) {
  try {
    const clientIP = getClientIP(event);
    const serverGeneratedHwid = generateHwidFromIP(clientIP);

    const { error: upsertError } = await supabase
      .from('users')
      .upsert({
        hwid: serverGeneratedHwid,
        status: 'neutral'
      }, {
        onConflict: 'hwid',
        ignoreDuplicates: true
      });

    if (upsertError) {
      // User creation failed silently
    }

    const { data, error } = await supabase
      .from('page_visits')
      .insert({
        hwid: serverGeneratedHwid,
        path
      });

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

async function checkUserStatus(_, event) {
  try {
    const clientIP = getClientIP(event);
    const serverGeneratedHwid = generateHwidFromIP(clientIP);



    const { data, error } = await supabase
      .from('blacklisted_hwids')
      .select('hwid')
      .eq('hwid', serverGeneratedHwid)
      .single();

    if (error && error.code !== 'PGRST116') {
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({ isBlacklisted: false, error: 'Could not check blacklist' })
      };
    }

    const isBlacklisted = !!data;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ isBlacklisted })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
}

async function checkAdminStatus(_, event) {
  try {
    const clientIP = getClientIP(event);
    const hwid = generateHwidFromIP(clientIP);

    const { data, error } = await supabase
      .from('admin_keys')
      .select('id, name')
      .eq('hwid', hwid)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    const isAdmin = !!data;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ isAdmin })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
}

async function adminLogin({ accessKey }, event) {
  try {
    if (!accessKey) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing access key' })
      };
    }

    const clientIP = getClientIP(event);
    const hwid = generateHwidFromIP(clientIP);

    const { data: keyData, error: keyError } = await supabase
      .from('admin_keys')
      .select('id, name, hwid')
      .eq('access_key', accessKey)
      .single();

    if (keyError || !keyData) {
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid access key' })
      };
    }

    if (!keyData.hwid) {
      const { error: updateError } = await supabase
        .from('admin_keys')
        .update({
          hwid: hwid,
          assigned_at: new Date().toISOString()
        })
        .eq('id', keyData.id);

      if (updateError) {
        return {
          statusCode: 500,
          headers: corsHeaders,
          body: JSON.stringify({ error: 'Failed to assign access key' })
        };
      }

      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          success: true,
          name: keyData.name,
          message: 'Access key assigned successfully'
        })
      };
    }

    if (keyData.hwid === hwid) {
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          success: true,
          name: keyData.name,
          message: 'Login successful'
        })
      };
    } else {
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Access key is assigned to a different device' })
      };
    }

  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
}

async function getAdminKeys(_, event) {
  try {
    const clientIP = getClientIP(event);
    const hwid = generateHwidFromIP(clientIP);

    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const { data, error } = await supabase
      .from('admin_keys')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
}

async function getScriptRequests(_, event) {
  try {
    const clientIP = getClientIP(event);
    const hwid = generateHwidFromIP(clientIP);

    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const { data, error } = await supabase
      .from('script_requests')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
}

async function updateRequestStatus({ requestId, status }, event) {
  try {
    if (!requestId || !status) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }

    const clientIP = getClientIP(event);
    const hwid = generateHwidFromIP(clientIP);

    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const { data, error } = await supabase
      .from('script_requests')
      .update({ status })
      .eq('id', requestId)
      .select()
      .single();

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
}

async function createScript({ scriptData }, event) {
  try {
    if (!scriptData) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }

    const clientIP = getClientIP(event);
    const hwid = generateHwidFromIP(clientIP);

    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const { data, error } = await supabase
      .from('scripts')
      .insert([scriptData])
      .select()
      .single();

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
}

async function updateScript({ scriptId, scriptData }, event) {
  try {
    if (!scriptId || !scriptData) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }

    const clientIP = getClientIP(event);
    const hwid = generateHwidFromIP(clientIP);

    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const { data, error } = await supabase
      .from('scripts')
      .update(scriptData)
      .eq('id', scriptId)
      .select()
      .single();

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
}

async function deleteScript({ scriptId }, event) {
  try {
    if (!scriptId) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }

    const clientIP = getClientIP(event);
    const hwid = generateHwidFromIP(clientIP);

    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const { error } = await supabase
      .from('scripts')
      .delete()
      .eq('id', scriptId);

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ success: true })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
}

async function getAdminStats(_, event) {
  try {
    const clientIP = getClientIP(event);
    const hwid = generateHwidFromIP(clientIP);

    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const [requestsResult, keysResult, visitsResult, scriptsResult] = await Promise.all([
      supabase.from('script_requests').select('id, created_at, status'),
      supabase.from('admin_keys').select('id'),
      supabase.from('page_visits').select('id, created_at'),
      supabase.from('scripts').select('id, created_at')
    ]);

    if (requestsResult.error) throw requestsResult.error;
    if (keysResult.error) throw keysResult.error;
    if (visitsResult.error) throw visitsResult.error;
    if (scriptsResult.error) throw scriptsResult.error;

    const requestsData = requestsResult.data || [];
    const keysData = keysResult.data || [];
    const visitsData = visitsResult.data || [];
    const scriptsData = scriptsResult.data || [];

    const monthlyData = requestsData.reduce((acc, req) => {
      const month = new Date(req.created_at).toLocaleString('default', { month: 'short', year: 'numeric' });
      acc[month] = (acc[month] || 0) + 1;
      return acc;
    }, {});
    const requestsChartData = Object.entries(monthlyData).map(([name, requests]) => ({ name, requests }));

    const statusData = requestsData.reduce((acc, req) => {
      acc[req.status] = (acc[req.status] || 0) + 1;
      return acc;
    }, {});
    const requestsStatusData = Object.entries(statusData).map(([name, value]) => ({ name, value }));

    const uniqueVisitors = new Set(visitsData.map(visit => visit.hwid || 'anonymous')).size;

    const stats = {
      requests: requestsData.length,
      keys: keysData.length,
      visits: uniqueVisitors,
      scripts: scriptsData.length,
      requestsChartData,
      requestsStatusData
    };

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ stats })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
}

