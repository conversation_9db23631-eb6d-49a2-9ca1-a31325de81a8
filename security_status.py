#!/usr/bin/env python3
"""
Bloxsync Security Status Checker
Provides a quick overview of current security status and remaining issues.
"""

import json
import sys
from pathlib import Path

def load_security_report():
    """Load the security report JSON file."""
    try:
        with open('security_report.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ Security report not found. Run 'python3 security_analyzer.py' first.")
        return None
    except json.JSONDecodeError:
        print("❌ Invalid security report format.")
        return None

def print_security_dashboard(report):
    """Print a security dashboard."""
    print("🛡️  BLOXSYNC SECURITY DASHBOARD")
    print("=" * 60)
    
    summary = report['summary']
    
    # Overall status
    critical_high = summary['by_severity'].get('CRITICAL', 0) + summary['by_severity'].get('HIGH', 0)
    
    if critical_high == 0:
        status = "🟢 SECURE"
        status_desc = "No critical or high severity issues found"
    elif critical_high <= 10:
        status = "🟡 NEEDS ATTENTION"
        status_desc = f"{critical_high} critical/high severity issues found"
    else:
        status = "🔴 VULNERABLE"
        status_desc = f"{critical_high} critical/high severity issues found"
    
    print(f"Security Status: {status}")
    print(f"Description: {status_desc}")
    print()
    
    # Severity breakdown
    print("📊 FINDINGS BREAKDOWN")
    print("-" * 30)
    for severity, count in summary['by_severity'].items():
        emoji = {
            'CRITICAL': '🔴',
            'HIGH': '🟠',
            'MEDIUM': '🟡',
            'LOW': '🔵',
            'INFO': '⚪'
        }.get(severity, '⚫')
        
        print(f"{emoji} {severity:8}: {count:3} issues")
    
    print(f"\nTotal Issues: {summary['total_findings']}")
    print()
    
    # Category breakdown
    print("📋 ISSUES BY CATEGORY")
    print("-" * 30)
    for category, count in summary['by_category'].items():
        print(f"• {category:20}: {count:3} issues")
    
    print()

def print_top_priorities(report):
    """Print top priority issues that need immediate attention."""
    print("🚨 TOP PRIORITY FIXES NEEDED")
    print("=" * 60)
    
    high_critical = [f for f in report['findings'] 
                    if f['severity'] in ['CRITICAL', 'HIGH']]
    
    # Group by category
    by_category = {}
    for finding in high_critical:
        category = finding['category']
        if category not in by_category:
            by_category[category] = []
        by_category[category].append(finding)
    
    priority_order = [
        'Client-Side Security',
        'Database Access', 
        'Hardcoded Secrets',
        'Environment Variables',
        'HWID Security',
        'Discord Webhook Exposure'
    ]
    
    for i, category in enumerate(priority_order, 1):
        if category in by_category:
            issues = by_category[category]
            print(f"{i}. {category} ({len(issues)} issues)")
            
            # Show example
            example = issues[0]
            print(f"   Example: {example['file_path']}:{example['line_number']}")
            print(f"   Issue: {example['description']}")
            print(f"   Fix: {example['recommendation']}")
            print()

def print_security_improvements():
    """Print security improvements already implemented."""
    print("✅ SECURITY IMPROVEMENTS IMPLEMENTED")
    print("=" * 60)
    
    improvements = [
        "Server-side HWID generation from IP addresses",
        "Blacklist system with server-side validation", 
        "Proper RLS policies in Supabase database",
        "Environment variables for sensitive configuration",
        "Security headers added to Netlify deployment",
        "Console.log statements removed from production code",
        "Secure API client with timeout and error handling",
        "Rate limiting middleware created"
    ]
    
    for improvement in improvements:
        print(f"✅ {improvement}")
    
    print()

def print_next_steps():
    """Print recommended next steps."""
    print("📋 RECOMMENDED NEXT STEPS")
    print("=" * 60)
    
    steps = [
        ("IMMEDIATE", [
            "Replace sessionStorage authentication with JWT tokens",
            "Move all admin operations to server-side functions",
            "Remove direct Supabase client usage from admin components",
            "Implement server-side admin validation"
        ]),
        ("THIS WEEK", [
            "Add authentication middleware to all admin routes",
            "Implement proper error handling without exposing internals",
            "Add request validation and sanitization",
            "Set up security monitoring and logging"
        ]),
        ("NEXT 2 WEEKS", [
            "Conduct penetration testing of admin functions",
            "Implement JWT token refresh mechanism",
            "Add CSRF protection to all forms",
            "Create security documentation and procedures"
        ])
    ]
    
    for timeframe, tasks in steps:
        print(f"\n{timeframe}:")
        for task in tasks:
            print(f"  • {task}")
    
    print()

def print_security_checklist():
    """Print a security checklist for manual verification."""
    print("☑️  MANUAL SECURITY CHECKLIST")
    print("=" * 60)
    
    checklist = [
        ("Authentication", [
            "Admin authentication is server-side only",
            "No sensitive data in sessionStorage/localStorage",
            "JWT tokens are properly validated",
            "Session timeout is implemented"
        ]),
        ("Database Security", [
            "RLS policies are properly configured",
            "No direct database access from client",
            "All queries go through server-side functions",
            "Sensitive data is properly encrypted"
        ]),
        ("API Security", [
            "All admin endpoints require authentication",
            "Input validation is implemented",
            "Rate limiting is in place",
            "Error messages don't expose internals"
        ]),
        ("Environment Security", [
            "No hardcoded secrets in code",
            "Environment variables are properly secured",
            "Production vs development configs are separate",
            "Secrets are not logged or exposed"
        ])
    ]
    
    for category, items in checklist:
        print(f"\n{category}:")
        for item in items:
            print(f"  ☐ {item}")
    
    print()

def main():
    report = load_security_report()
    if not report:
        return
    
    print_security_dashboard(report)
    print_top_priorities(report)
    print_security_improvements()
    print_next_steps()
    print_security_checklist()
    
    print("🔗 ADDITIONAL RESOURCES")
    print("=" * 60)
    print("• Detailed Report: security_report.json")
    print("• Security Analysis: SECURITY_REPORT.md")
    print("• Run Analyzer: python3 security_analyzer.py")
    print("• Apply Fixes: python3 security_fixes.py")
    print()
    
    # Final recommendation
    critical_high = report['summary']['by_severity'].get('CRITICAL', 0) + report['summary']['by_severity'].get('HIGH', 0)
    if critical_high > 0:
        print(f"⚠️  RECOMMENDATION: Address {critical_high} critical/high severity issues before production deployment.")
    else:
        print("✅ RECOMMENDATION: Security posture is good. Continue monitoring and regular security reviews.")

if __name__ == "__main__":
    main()
